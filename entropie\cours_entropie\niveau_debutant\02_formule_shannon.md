# 🟢 NIVEAU DÉBUTANT - Chapitre 2
## La Formule de Shannon : Calculer l'Entropie

### 🎯 Objectifs de ce chapitre
- Comprendre la formule mathématique de l'entropie
- Apprendre à calculer l'entropie étape par étape
- Interpréter les résultats obtenus
- Maîtriser les cas particuliers importants

---

## 📐 La Formule Fondamentale

### Formule de l'Entropie de Shannon

Pour un système avec des événements possibles ayant des probabilités p₁, p₂, ..., pₙ :

```
H = -∑ pᵢ × log₂(pᵢ)
```

**En français** : L'entropie H est la somme de tous les termes (-pᵢ × log₂(pᵢ))

### Décomposition de la Formule

**Chaque symbole expliqué** :

- **H** : L'entropie (résultat final)
- **∑** : Symbole de somme (on additionne tous les termes)
- **pᵢ** : Probabilité de l'événement i
- **log₂** : Logarithme en base 2
- **-** : Signe moins (pour obtenir un résultat positif)

### Pourquoi cette Formule ?

1. **Le logarithme** : Transforme les probabilités en "quantité d'information"
2. **La base 2** : Donne le résultat en "bits" d'information
3. **Le signe moins** : Rend le résultat positif (car log₂(p) ≤ 0 pour p ≤ 1)
4. **La pondération par pᵢ** : Donne plus d'importance aux événements fréquents

---

## 🧮 Calcul Étape par Étape

### Exemple 1 : Pièce de Monnaie Équilibrée

**Situation** : Pile ou Face avec probabilités égales

**Étape 1** : Identifier les probabilités
- p₁ = 0.5 (probabilité de Pile)
- p₂ = 0.5 (probabilité de Face)

**Étape 2** : Calculer chaque terme
- Terme 1 : -p₁ × log₂(p₁) = -0.5 × log₂(0.5) = -0.5 × (-1) = 0.5
- Terme 2 : -p₂ × log₂(p₂) = -0.5 × log₂(0.5) = -0.5 × (-1) = 0.5

**Étape 3** : Additionner
- H = 0.5 + 0.5 = **1 bit**

**Interprétation** : Il faut 1 bit d'information pour décrire le résultat.

### Exemple 2 : Dé Équilibré (6 faces)

**Situation** : Dé avec 6 faces équiprobables

**Étape 1** : Probabilités
- Chaque face : p = 1/6 ≈ 0.167

**Étape 2** : Calcul d'un terme
- -p × log₂(p) = -(1/6) × log₂(1/6) = -(1/6) × (-2.58) ≈ 0.43

**Étape 3** : Multiplication par 6
- H = 6 × 0.43 = **2.58 bits**

**Interprétation** : Il faut environ 2.6 bits pour décrire le résultat d'un dé.

### Exemple 3 : Dé Truqué

**Situation** : Dé où le 6 sort 50% du temps, les autres faces 10% chacune

**Étape 1** : Probabilités
- p₆ = 0.5 (face 6)
- p₁ = p₂ = p₃ = p₄ = p₅ = 0.1 (autres faces)

**Étape 2** : Calculs
- Terme pour le 6 : -0.5 × log₂(0.5) = 0.5
- Terme pour chaque autre face : -0.1 × log₂(0.1) = -0.1 × (-3.32) = 0.332

**Étape 3** : Somme totale
- H = 0.5 + 5 × 0.332 = 0.5 + 1.66 = **2.16 bits**

**Observation** : L'entropie est plus faible que pour le dé équilibré (2.16 < 2.58).

---

## 📊 Cas Particuliers Importants

### Cas 1 : Certitude Absolue
**Situation** : Un seul événement possible (probabilité = 1)

```
H = -1 × log₂(1) = -1 × 0 = 0 bit
```

**Exemple** : Prédire que le soleil se lèvera demain
**Interprétation** : Aucune information nécessaire (résultat certain).

### Cas 2 : Distribution Uniforme
**Situation** : n événements équiprobables (probabilité = 1/n chacun)

```
H = n × (-(1/n) × log₂(1/n)) = log₂(n)
```

**Exemples** :
- 2 événements équiprobables : H = log₂(2) = 1 bit
- 4 événements équiprobables : H = log₂(4) = 2 bits
- 8 événements équiprobables : H = log₂(8) = 3 bits

**Règle** : Pour n = 2ᵏ événements équiprobables, H = k bits exactement.

### Cas 3 : Événement Très Rare
**Situation** : Un événement très probable, les autres très rares

**Exemple** : p₁ = 0.99, p₂ = 0.01
- H = -0.99 × log₂(0.99) - 0.01 × log₂(0.01)
- H = -0.99 × (-0.014) - 0.01 × (-6.64)
- H = 0.014 + 0.066 = **0.08 bit**

**Interprétation** : Très peu d'incertitude, donc très peu d'information nécessaire.

---

## 🎯 Exercices Pratiques

### Exercice 1 : Calcul Simple
Calculez l'entropie d'un système avec 3 événements équiprobables.

**Solution** :
- Chaque probabilité : p = 1/3
- H = 3 × (-(1/3) × log₂(1/3)) = log₂(3) ≈ **1.58 bits**

### Exercice 2 : Comparaison
Comparez l'entropie de ces deux systèmes :
- **Système A** : p₁ = 0.8, p₂ = 0.2
- **Système B** : p₁ = 0.6, p₂ = 0.4

**Solution A** :
H_A = -0.8 × log₂(0.8) - 0.2 × log₂(0.2) = 0.32 + 0.46 = **0.78 bit**

**Solution B** :
H_B = -0.6 × log₂(0.6) - 0.4 × log₂(0.4) = 0.44 + 0.53 = **0.97 bit**

**Conclusion** : Le système B a plus d'entropie (plus d'incertitude).

### Exercice 3 : Application Réelle
Une station météo prédit :
- Soleil : 60%
- Nuages : 30%
- Pluie : 10%

Calculez l'entropie de cette prévision.

---

## 🔍 Interprétation des Résultats

### Échelle de Valeurs

```
0 bit ────────────────────── log₂(n) bits
  │                              │
Certitude                   Incertitude
absolue                      maximale
  │                              │
1 seul                      n événements
événement                   équiprobables
```

### Règles d'Interprétation

1. **H = 0** : Système déterministe (aucune surprise)
2. **H faible** : Système prévisible (peu de surprise)
3. **H élevée** : Système imprévisible (beaucoup de surprise)
4. **H = log₂(n)** : Incertitude maximale pour n événements

### Applications Pratiques

**Compression de fichiers** :
- H faible → Compression efficace possible
- H élevée → Compression difficile

**Sécurité** :
- H faible → Mot de passe faible
- H élevée → Mot de passe fort

**Communication** :
- H faible → Message redondant
- H élevée → Message informatif

---

## 🧪 Propriétés Importantes

### Propriété 1 : Positivité
L'entropie est toujours positive ou nulle : H ≥ 0

### Propriété 2 : Maximum
Pour n événements, l'entropie maximale est log₂(n), atteinte quand tous les événements sont équiprobables.

### Propriété 3 : Additivité
Pour des événements indépendants, les entropies s'additionnent.

### Propriété 4 : Continuité
De petits changements dans les probabilités entraînent de petits changements dans l'entropie.

---

## 🔗 Vers le Chapitre Suivant

Dans le prochain chapitre, nous découvrirons :
- **L'entropie conditionnelle** : mesurer l'incertitude sachant une information
- **L'information mutuelle** : mesurer la dépendance entre variables
- **Des applications** en communication et apprentissage automatique

---

## 📚 Points Clés à Retenir

✅ **La formule** : H = -∑ pᵢ × log₂(pᵢ)  
✅ **L'unité** : L'entropie se mesure en bits  
✅ **Les extrêmes** : 0 ≤ H ≤ log₂(n)  
✅ **L'interprétation** : Plus H est grand, plus il y a d'incertitude  
✅ **Le calcul** : Sommer tous les termes -pᵢ × log₂(pᵢ)  

---

*Prochaine étape : [Chapitre 3 - Entropie Conditionnelle et Information Mutuelle](03_entropie_conditionnelle.md)*
