"""
ANALYSEUR D'ENTROPIE BACCARAT - INDEX5
=====================================

Programme d'analyse de l'évolution de l'entropie pour l'INDEX5 au cours d'une partie de baccarat.
Basé sur les formules d'entropie de Shannon du dossier entropie/cours_entropie.

Référence: entropie/cours_entropie/niveau_debutant/02_formule_shannon.md
Formule: H(X) = -∑ p(x) log₂ p(x)

Auteur: Analyse Entropie Baccarat
Date: 2025-01-06
"""

import json
import math
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
import warnings
from collections import Counter
import seaborn as sns

# Configuration pour éviter les warnings de log(0)
np.seterr(divide='ignore', invalid='ignore')
warnings.filterwarnings('ignore')

class BaccaratEntropyAnalyzer:
    """
    Analyseur d'entropie pour le baccarat basé sur les formules de Shannon.
    
    Référence: entropie/cours_entropie/ressources/implementations_python.py
    Adapté pour l'analyse de l'INDEX5 du baccarat.
    """
    
    def __init__(self, base: float = 2.0, epsilon: float = 1e-12):
        """
        Initialise l'analyseur d'entropie pour le baccarat.
        
        Args:
            base: Base du logarithme (2 pour bits, e pour nats)
            epsilon: Valeur minimale pour éviter log(0)
        """
        self.base = base
        self.epsilon = epsilon
        
        # Probabilités théoriques INDEX5 EXACTES (en pourcentage)
        # CORRECTION EXPERTE: Utilisation des vraies probabilités calculées sur données réelles
        self.theoretical_probs = {
            '0_A_BANKER': 8.5136, '1_A_BANKER': 8.6389,
            '0_B_BANKER': 6.4676, '1_B_BANKER': 6.5479,  # CORRIGÉ: était 7.6907
            '0_C_BANKER': 7.7903, '1_C_BANKER': 7.8929,
            '0_A_PLAYER': 8.5240, '1_A_PLAYER': 8.6361,
            '0_B_PLAYER': 7.6907, '1_B_PLAYER': 7.7888,
            '0_C_PLAYER': 5.9617, '1_C_PLAYER': 6.0352,
            '0_A_TIE': 1.7719, '1_A_TIE': 1.7978,
            '0_B_TIE': 1.6281, '1_B_TIE': 1.6482,
            '0_C_TIE': 1.3241, '1_C_TIE': 1.3423
        }
        
        # Normalisation des probabilités
        total = sum(self.theoretical_probs.values())
        self.theoretical_probs = {k: v/total for k, v in self.theoretical_probs.items()}
        
        # Calcul de l'entropie théorique maximale
        self.theoretical_entropy = self._calculate_shannon_entropy(list(self.theoretical_probs.values()))
        
        print(f"🎯 Entropie théorique INDEX5: {self.theoretical_entropy:.4f} bits")
        
    def _safe_log(self, x: np.ndarray) -> np.ndarray:
        """
        Calcul sécurisé du logarithme avec gestion de log(0).
        
        Référence: entropie/cours_entropie/ressources/implementations_python.py
        """
        x = np.array(x)
        x = np.where(x <= 0, self.epsilon, x)
        return np.log(x) / np.log(self.base)
    
    def _validate_probabilities(self, p: np.ndarray) -> np.ndarray:
        """
        Valide et normalise un vecteur de probabilités.
        
        Référence: entropie/cours_entropie/ressources/implementations_python.py
        """
        p = np.array(p, dtype=float)
        
        if np.any(p < 0):
            raise ValueError("Les probabilités doivent être positives")
        
        total = np.sum(p)
        if total > 0:
            p = p / total
        else:
            # Distribution uniforme si toutes les probabilités sont nulles
            p = np.ones_like(p) / len(p)
            
        return p

    def _calculate_shannon_entropy(self, probabilities: List[float]) -> float:
        """
        Calcule l'entropie de Shannon: H(X) = -∑ p(x) log₂ p(x)
        
        Référence: entropie/cours_entropie/niveau_debutant/02_formule_shannon.md
        
        Args:
            probabilities: Liste des probabilités
            
        Returns:
            Entropie de Shannon en bits
        """
        p = self._validate_probabilities(probabilities)
        
        # Calcul avec gestion de 0*log(0) = 0
        log_p = self._safe_log(p)
        entropy_terms = p * log_p
        
        # Remplace NaN par 0 (cas 0*log(0))
        entropy_terms = np.where(p == 0, 0, entropy_terms)
        
        return -np.sum(entropy_terms)

    def load_baccarat_data(self, filepath: str) -> List[Dict]:
        """
        Charge les données de baccarat depuis le fichier JSON.

        Args:
            filepath: Chemin vers le fichier JSON

        Returns:
            Liste des parties de baccarat
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Vérifier la structure du JSON
            if isinstance(data, dict) and 'parties_condensees' in data:
                # Structure: {"parties_condensees": [...]}
                parties = data['parties_condensees']
                print(f"✅ Données chargées: {len(parties)} parties trouvées")
                return parties
            elif isinstance(data, list):
                # Structure: [partie1, partie2, ...]
                print(f"✅ Données chargées: {len(data)} parties trouvées")
                return data
            else:
                print(f"❌ Structure JSON non reconnue. Clés disponibles: {list(data.keys()) if isinstance(data, dict) else 'Liste non détectée'}")
                return []

        except FileNotFoundError:
            print(f"❌ Erreur: Fichier {filepath} non trouvé")
            return []
        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON: {e}")
            return []

    def extract_index5_sequence(self, game_data: Dict) -> List[str]:
        """
        Extrait la séquence INDEX5 d'une partie en excluant les mains d'ajustement.

        Args:
            game_data: Données d'une partie

        Returns:
            Séquence des valeurs INDEX5 (sans les mains d'ajustement)
        """
        sequence = []

        # Vérifier différentes structures possibles
        if 'hands' in game_data:
            # Structure: {"hands": [...]}
            for hand in game_data['hands']:
                # Exclure les mains d'ajustement (main_number null ou INDEX5 vide)
                if (hand.get('main_number') is not None and
                    'INDEX5' in hand and
                    hand['INDEX5'] and
                    hand['INDEX5'].strip()):
                    sequence.append(hand['INDEX5'])

        elif 'mains_condensees' in game_data:
            # Structure: {"mains_condensees": [...]}
            for main in game_data['mains_condensees']:
                # Exclure les mains d'ajustement (main_number null ou index5 vide)
                if (main.get('main_number') is not None and
                    'index5' in main and
                    main['index5'] and
                    main['index5'].strip()):
                    sequence.append(main['index5'])
        else:
            print(f"❌ Structure de partie non reconnue. Clés disponibles: {list(game_data.keys())}")
            return []

        print(f"🔍 Séquence extraite: {len(sequence)} mains valides (mains d'ajustement exclues)")

        return sequence

    def calculate_block_entropy_evolution(self, sequence: List[str], max_block_length: int = 5) -> List[Dict]:
        """
        Calcule l'évolution de l'entropie par blocs selon la méthode de Kolmogorov-Sinai.

        Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
        Méthode: entropie/cours_entropie/ressources/implementations_python.py (metric_entropy_estimate)

        Args:
            sequence: Séquence des valeurs INDEX5
            max_block_length: Longueur maximale des blocs à analyser

        Returns:
            Liste des résultats d'entropie pour chaque position
        """
        results = []

        for n in range(1, len(sequence) + 1):
            # Sous-séquence de longueur n
            subsequence = sequence[:n]

            # Calcul de l'entropie de blocs pour différentes longueurs
            block_entropies = self._calculate_block_entropies(subsequence, max_block_length)

            # Entropie conditionnelle (prédictibilité du prochain symbole)
            conditional_entropy = self._calculate_conditional_entropy(subsequence)

            # Estimation de l'entropie métrique (taux de création d'information)
            metric_entropy = self._estimate_metric_entropy(subsequence, max_block_length)

            # Comptage simple pour comparaison (fréquences observées)
            counts = Counter(subsequence)
            total = len(subsequence)
            empirical_probs = [counts[value] / total for value in counts.keys()]

            # CORRECTION: Entropie simple observée basée sur fréquences empiriques
            # Calcul de l'entropie de Shannon sur les fréquences observées dans la sous-séquence
            simple_entropy_observed = self._calculate_shannon_entropy(empirical_probs)

            # CORRECTION: Entropie simple avec probabilités théoriques (formule AEP)
            simple_entropy_theoretical = self._calculate_sequence_entropy_aep(subsequence)

            results.append({
                'position': n,
                'sequence_length': n,
                'unique_values': len(counts),
                'simple_entropy': simple_entropy_observed,  # Entropie de Shannon sur fréquences observées
                'simple_entropy_theoretical': simple_entropy_theoretical,  # Entropie AEP avec probabilités théoriques
                'block_entropies': block_entropies,  # H(blocs de longueur k)
                'conditional_entropy': conditional_entropy,  # H(Xₙ|X₁,...,Xₙ₋₁)
                'metric_entropy': metric_entropy,  # Estimation h_μ(T)
                'entropy_rate': block_entropies[-1] if block_entropies else 0,  # Taux d'entropie
                'observed_values': list(counts.keys()),
                'counts': dict(counts),
                'empirical_probabilities': dict(zip(counts.keys(), empirical_probs))
            })

        return results

    def _calculate_block_entropies(self, sequence: List[str], max_length: int) -> List[float]:
        """
        Calcule l'entropie pour des blocs de différentes longueurs.
        CORRECTION: Utilise les probabilités théoriques pour les blocs de longueur 1

        Référence: entropie/cours_entropie/ressources/implementations_python.py
        Méthode de l'entropie métrique par blocs.
        """
        if len(sequence) < 2:
            return [0.0]

        entropies = []

        for block_len in range(1, min(max_length + 1, len(sequence) + 1)):
            if block_len == 1:
                # CORRECTION AEP: Pour les blocs de longueur 1, calculer l'entropie de la séquence complète
                # selon la formule AEP au lieu d'utiliser toutes les probabilités théoriques
                block_entropy = self._calculate_sequence_entropy_aep(sequence)
                entropies.append(block_entropy)
            else:
                # Pour les blocs de longueur > 1, créer des sous-séquences et utiliser AEP
                block_sequences = []
                for i in range(len(sequence) - block_len + 1):
                    block_sequence = sequence[i:i+block_len]
                    block_sequences.append(block_sequence)

                if not block_sequences:
                    entropies.append(0.0)
                    continue

                # CORRECTION AEP: Calculer l'entropie moyenne des blocs selon AEP
                total_entropy = 0.0
                for block_seq in block_sequences:
                    total_entropy += self._calculate_sequence_entropy_aep(block_seq)

                block_entropy = total_entropy / len(block_sequences) if block_sequences else 0.0
                entropies.append(block_entropy)

        return entropies

    def _calculate_block_entropies_raw(self, sequence: List[str], max_length: int) -> List[float]:
        """
        Calcule l'entropie pour des blocs de différentes longueurs SANS normalisation.
        NOUVELLE MÉTHODE: Pour le calcul correct de l'entropie métrique.

        Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
        """
        if len(sequence) < 2:
            return [0.0]

        entropies = []

        for block_len in range(1, min(max_length + 1, len(sequence) + 1)):
            # CORRECTION AEP: Créer toutes les sous-séquences de cette longueur
            block_sequences = []
            for i in range(len(sequence) - block_len + 1):
                block_sequence = sequence[i:i+block_len]
                block_sequences.append(block_sequence)

            if not block_sequences:
                entropies.append(0.0)
                continue

            # CORRECTION AEP: Calculer l'entropie moyenne des blocs selon la formule AEP
            total_entropy = 0.0
            for block_seq in block_sequences:
                total_entropy += self._calculate_sequence_entropy_aep(block_seq)

            # Entropie moyenne des blocs de cette longueur
            block_entropy = total_entropy / len(block_sequences) if block_sequences else 0.0
            entropies.append(block_entropy)

        return entropies

    def _calculate_sequence_entropy_aep(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie d'une séquence selon la formule AEP exacte :
        H_séquence = -(1/n) × log₂ p(X₁, X₂, ..., Xₙ)
        où p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ) pour séquences indépendantes

        Référence: Elements of Information Theory - ligne 1919
        "The AEP states that (1/n) log (1/p(X₁, X₂, ..., Xₙ)) is close to the entropy H"

        Cette formule unifiée remplace tous les calculs d'entropie de séquence
        pour garantir la cohérence mathématique selon la théorie de l'information.

        Args:
            sequence: Liste des valeurs INDEX5 dans la séquence

        Returns:
            float: Entropie de la séquence selon AEP en bits
        """
        if not sequence:
            return 0.0

        # Calculer -log₂ de la probabilité jointe théorique
        # p(séquence) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)
        # log₂(p(séquence)) = ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
        total_log_prob = 0.0
        for value in sequence:
            if value in self.theoretical_probs:
                p_theo = self.theoretical_probs[value]
                if p_theo > 0:
                    total_log_prob += math.log2(p_theo)
                else:
                    # Si probabilité théorique = 0, utiliser une valeur très faible
                    total_log_prob += math.log2(self.epsilon)
            else:
                # Si valeur non trouvée dans les probabilités théoriques, utiliser une probabilité très faible
                total_log_prob += math.log2(self.epsilon)

        # Retourner l'entropie de la séquence : -(1/n) × ∑log₂(p_théo(xᵢ))
        return -total_log_prob / len(sequence)

    def _calculate_simple_entropy_theoretical(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie théorique simple - WRAPPER vers la méthode AEP unifiée.
        Maintenu pour compatibilité avec le code existant.
        """
        return self._calculate_sequence_entropy_aep(sequence)

    def _calculate_conditional_entropy(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie conditionnelle H(Xₙ|X₁,...,Xₙ₋₁) avec contexte fixe.
        CORRECTION EXPERTE: Utilise un contexte de longueur fixe pour cohérence mathématique.

        Référence: entropie/cours_entropie/niveau_debutant/03_entropie_conditionnelle.md
        Formule: H(X|Y) = ∑ P(y) × H(X|y)
        """
        if len(sequence) < 2:
            return 0.0

        # CORRECTION: Utiliser un contexte de longueur fixe (longueur 1 pour simplicité)
        # Cela donne H(Xₙ|Xₙ₋₁) au lieu de mélanger différentes longueurs
        context_length = min(1, len(sequence) - 1)

        # Compter les transitions contexte → symbole suivant
        context_transitions = {}

        for i in range(len(sequence) - context_length):
            if context_length == 1:
                context = sequence[i]  # Contexte de longueur 1
            else:
                context = tuple(sequence[i:i+context_length])

            next_symbol = sequence[i+context_length]

            if context not in context_transitions:
                context_transitions[context] = {}

            if next_symbol not in context_transitions[context]:
                context_transitions[context][next_symbol] = 0
            context_transitions[context][next_symbol] += 1

        if not context_transitions:
            return 0.0

        # Calculer H(X|Contexte) = ∑ P(contexte) × H(X|contexte)
        total_transitions = sum(sum(transitions.values()) for transitions in context_transitions.values())
        conditional_entropy = 0.0

        for context, transitions in context_transitions.items():
            context_prob = sum(transitions.values()) / total_transitions

            # CORRECTION AEP: H(X|ce contexte) calculé selon AEP
            # Créer la séquence des symboles suivants pour ce contexte
            context_sequence = []
            for next_symbol, count in transitions.items():
                context_sequence.extend([next_symbol] * count)

            context_entropy = self._calculate_sequence_entropy_aep(context_sequence)
            conditional_entropy += context_prob * context_entropy

        return conditional_entropy

    def _estimate_metric_entropy(self, sequence: List[str], max_length: int) -> float:
        """
        Estime l'entropie métrique h_μ(T) selon Kolmogorov-Sinai.
        CORRECTION EXPERTE: Calcul rigoureux de la limite h_μ(T) = lim H(n)/n.

        Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
        Formule: h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
        """
        if len(sequence) < 3:
            return 0.0

        # CORRECTION: Calculer les entropies de blocs sans normalisation par longueur
        block_entropies_raw = self._calculate_block_entropies_raw(sequence, max_length)

        if len(block_entropies_raw) < 2:
            return 0.0

        # CORRECTION EXPERTE: Calcul rigoureux de h_μ = lim_{n→∞} H(n)/n
        # Formule correcte: h_μ(T) = lim H(n)/n selon Kolmogorov-Sinai
        h_metric_estimates = []
        for i, entropy in enumerate(block_entropies_raw):
            block_length = i + 1  # Longueur du bloc (commence à 1)
            if block_length > 0:
                h_estimate = entropy / block_length  # H(n)/n
                h_metric_estimates.append(h_estimate)

        # Prendre la dernière estimation comme approximation de la limite
        if h_metric_estimates:
            h_metric = max(0.0, h_metric_estimates[-1])
        else:
            h_metric = 0.0

        return h_metric

    def analyze_single_game(self, game_data: Dict, game_id: Optional[str] = None) -> Dict:
        """
        Analyse complète d'une seule partie selon les méthodes avancées d'entropie.

        Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md

        Args:
            game_data: Données de la partie
            game_id: Identifiant de la partie (optionnel)

        Returns:
            Résultats d'analyse complets avec entropie métrique
        """
        sequence = self.extract_index5_sequence(game_data)

        if not sequence:
            return {'error': 'Aucune séquence INDEX5 trouvée'}

        # Nouvelle méthode : entropie de blocs et métrique
        entropy_evolution = self.calculate_block_entropy_evolution(sequence, max_block_length=4)

        if not entropy_evolution:
            return {'error': 'Impossible de calculer l\'évolution d\'entropie'}

        # Extraction des métriques finales
        final_analysis = entropy_evolution[-1]

        # Calcul de la complexité de la séquence
        complexity_metrics = self._calculate_sequence_complexity(sequence)

        return {
            'game_id': game_id or 'Unknown',
            'sequence_length': len(sequence),
            'full_sequence': sequence,
            'entropy_evolution': entropy_evolution,

            # Métriques finales (nouvelle approche)
            'final_metric_entropy': final_analysis['metric_entropy'],
            'final_conditional_entropy': final_analysis['conditional_entropy'],
            'final_entropy_rate': final_analysis['entropy_rate'],
            'final_simple_entropy': final_analysis['simple_entropy'],  # Ancienne méthode

            # Analyse de complexité
            'complexity_metrics': complexity_metrics,

            # Positions d'intérêt
            'max_metric_entropy_position': max(entropy_evolution, key=lambda x: x['metric_entropy'])['position'] if entropy_evolution else 0,
            'max_conditional_entropy_position': max(entropy_evolution, key=lambda x: x['conditional_entropy'])['position'] if entropy_evolution else 0
        }

    def _calculate_sequence_complexity(self, sequence: List[str]) -> Dict:
        """
        Calcule diverses métriques de complexité de la séquence.

        Référence: entropie/cours_entropie/niveau_expert/02_entropie_topologique.md
        """
        n = len(sequence)

        # Nombre de motifs uniques de différentes longueurs
        unique_patterns = {}
        for length in range(1, min(6, n + 1)):
            patterns = set()
            for i in range(n - length + 1):
                pattern = tuple(sequence[i:i+length])
                patterns.add(pattern)
            unique_patterns[f'length_{length}'] = len(patterns)

        # Complexité de Lempel-Ziv (approximation)
        lz_complexity = self._approximate_lz_complexity(sequence)

        # Entropie topologique approximée
        topological_entropy = self._approximate_topological_entropy(sequence)

        return {
            'unique_patterns': unique_patterns,
            'lz_complexity': lz_complexity,
            'topological_entropy': topological_entropy,
            'sequence_diversity': len(set(sequence)) / len(self.theoretical_probs),  # Diversité relative
            'repetition_rate': self._calculate_repetition_rate(sequence)
        }

    def _approximate_lz_complexity(self, sequence: List[str]) -> int:
        """
        Approximation de la complexité de Lempel-Ziv.

        Référence: entropie/cours_entropie/niveau_intermediaire/02_codage_source.md
        """
        if not sequence:
            return 0

        dictionary = set()
        i = 0
        complexity = 0

        while i < len(sequence):
            # Chercher le plus long préfixe non vu
            for length in range(1, len(sequence) - i + 1):
                substring = tuple(sequence[i:i+length])
                if substring not in dictionary:
                    dictionary.add(substring)
                    complexity += 1
                    i += length
                    break
            else:
                # Tous les préfixes sont dans le dictionnaire
                i += 1
                complexity += 1

        return complexity

    def _approximate_topological_entropy(self, sequence: List[str]) -> float:
        """
        Approximation de l'entropie topologique respectant le principe variationnel.
        CORRECTION EXPERTE: Assure h_top ≥ h_μ selon le principe variationnel.

        Référence: entropie/cours_entropie/niveau_expert/02_entropie_topologique.md
        Principe: h_top(T) = sup{h_μ(T) : μ T-invariante} ≥ h_μ(T)
        """
        if len(sequence) < 2:
            return 0.0

        # Compter les motifs uniques de longueurs croissantes
        max_length = min(5, len(sequence))
        pattern_counts = []

        for length in range(1, max_length + 1):
            patterns = set()
            for i in range(len(sequence) - length + 1):
                pattern = tuple(sequence[i:i+length])
                patterns.add(pattern)
            pattern_counts.append(len(patterns))

        if len(pattern_counts) < 2:
            return 0.0

        # CORRECTION EXPERTE: Estimation correcte respectant le principe variationnel
        # h_top = lim_{n→∞} (1/n) log(N(n)) où N(n) = nombre de motifs de longueur n
        # Formule correcte: (1/n) log(N(n))
        growth_rates = []
        for i in range(len(pattern_counts)):
            length = i + 1  # Longueur du motif (commence à 1)
            if pattern_counts[i] > 0:
                growth_rate = self._safe_log(np.array([pattern_counts[i]]))[0] / length
                growth_rates.append(growth_rate)

        if growth_rates:
            # Prendre le maximum pour respecter le principe variationnel
            h_top_estimate = max(growth_rates)
        else:
            h_top_estimate = self._safe_log(np.array([pattern_counts[-1]]))[0] / max_length

        # CORRECTION: Assurer h_top ≥ h_μ (principe variationnel)
        # Calculer une estimation rapide de h_μ pour comparaison
        if len(sequence) >= 3:
            h_metric_estimate = self._estimate_metric_entropy(sequence, 3)
            h_top_estimate = max(h_top_estimate, h_metric_estimate * 1.1)  # Marge de sécurité

        return h_top_estimate

    def _calculate_repetition_rate(self, sequence: List[str]) -> float:
        """
        Calcule le taux de répétition dans la séquence.
        """
        if len(sequence) < 2:
            return 0.0

        # Compter les répétitions immédiates
        repetitions = 0
        for i in range(len(sequence) - 1):
            if sequence[i] == sequence[i + 1]:
                repetitions += 1

        return repetitions / (len(sequence) - 1)

    def plot_entropy_evolution(self, analysis_result: Dict, save_path: Optional[str] = None):
        """
        Visualise l'évolution de l'entropie au cours d'une partie.

        Args:
            analysis_result: Résultats d'analyse d'une partie
            save_path: Chemin pour sauvegarder le graphique (optionnel)
        """
        if 'entropy_evolution' not in analysis_result:
            print("❌ Pas de données d'évolution d'entropie")
            return

        evolution = analysis_result['entropy_evolution']
        positions = [item['position'] for item in evolution]
        # CORRECTION: Utiliser 'simple_entropy' au lieu de 'empirical_entropy' (fréquences observées = entropie empirique)
        empirical_entropies = [item['simple_entropy'] for item in evolution]
        unique_values = [item['unique_values'] for item in evolution]

        # Configuration du graphique
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # Graphique 1: Évolution de l'entropie
        ax1.plot(positions, empirical_entropies, 'b-', linewidth=2, marker='o', markersize=4, label='Entropie empirique')
        ax1.axhline(y=self.theoretical_entropy, color='r', linestyle='--', linewidth=2, label=f'Entropie théorique max ({self.theoretical_entropy:.3f} bits)')
        ax1.set_xlabel('Position dans la partie (main n)')
        ax1.set_ylabel('Entropie (bits)')
        ax1.set_title(f'Évolution de l\'Entropie INDEX5 - Partie {analysis_result["game_id"]}')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Graphique 2: Nombre de valeurs uniques
        ax2.plot(positions, unique_values, 'g-', linewidth=2, marker='s', markersize=4, label='Valeurs uniques observées')
        ax2.axhline(y=18, color='r', linestyle='--', linewidth=2, label='Maximum théorique (18 valeurs)')
        ax2.set_xlabel('Position dans la partie (main n)')
        ax2.set_ylabel('Nombre de valeurs uniques')
        ax2.set_title('Évolution de la Diversité des Valeurs INDEX5')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', format='jpg')
            print(f"📊 Graphique sauvegardé: {save_path}")

        plt.show()

    def generate_entropy_report(self, analysis_result: Dict) -> str:
        """
        Génère un rapport détaillé d'analyse d'entropie selon les méthodes avancées.

        Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md

        Args:
            analysis_result: Résultats d'analyse d'une partie

        Returns:
            Rapport formaté en texte avec métriques avancées
        """
        if 'entropy_evolution' not in analysis_result:
            return "❌ Pas de données d'analyse disponibles"

        evolution = analysis_result['entropy_evolution']
        game_id = analysis_result['game_id']
        sequence_length = analysis_result['sequence_length']
        complexity = analysis_result.get('complexity_metrics', {})

        # Statistiques des nouvelles métriques
        metric_entropies = [item['metric_entropy'] for item in evolution]
        conditional_entropies = [item['conditional_entropy'] for item in evolution]
        entropy_rates = [item['entropy_rate'] for item in evolution]
        simple_entropies = [item['simple_entropy'] for item in evolution]  # Ancienne méthode

        # Valeurs finales
        final_metric = analysis_result.get('final_metric_entropy', 0)
        final_conditional = analysis_result.get('final_conditional_entropy', 0)
        final_rate = analysis_result.get('final_entropy_rate', 0)
        final_simple = analysis_result.get('final_simple_entropy', 0)

        # Positions des maxima
        max_metric_pos = analysis_result.get('max_metric_entropy_position', 0)
        max_conditional_pos = analysis_result.get('max_conditional_entropy_position', 0)

        report = f"""
🎯 RAPPORT D'ANALYSE D'ENTROPIE AVANCÉE - INDEX5
===============================================
Méthodes: Kolmogorov-Sinai, Entropie de Blocs, Entropie Conditionnelle
Référence: entropie/cours_entropie/niveau_expert/

📊 INFORMATIONS GÉNÉRALES
Partie ID: {game_id}
Longueur de la séquence: {sequence_length} mains
Entropie théorique maximale: {self.theoretical_entropy:.4f} bits

📈 MÉTRIQUES D'ENTROPIE AVANCÉES
┌─ Entropie Métrique (Kolmogorov-Sinai) ─┐
│ Finale: {final_metric:.4f} bits/symbole │
│ Maximum: {max(metric_entropies):.4f} bits (position {max_metric_pos}) │
│ Moyenne: {np.mean(metric_entropies):.4f} bits │
└────────────────────────────────────────┘

┌─ Entropie Conditionnelle H(Xₙ|X₁...Xₙ₋₁) ─┐
│ Finale: {final_conditional:.4f} bits │
│ Maximum: {max(conditional_entropies):.4f} bits (position {max_conditional_pos}) │
│ Moyenne: {np.mean(conditional_entropies):.4f} bits │
│ → Mesure la prédictibilité du prochain symbole │
└─────────────────────────────────────────────┘

┌─ Taux d'Entropie (Entropy Rate) ─┐
│ Final: {final_rate:.4f} bits/symbole │
│ → Limite asymptotique de l'information par symbole │
└──────────────────────────────────┘

📊 COMPARAISON DES MÉTHODES D'ENTROPIE
Entropie simple (fréquences observées): {final_simple:.4f} bits
Entropie simple (probabilités théoriques): {evolution[-1].get('simple_entropy_theoretical', 0):.4f} bits
Entropie métrique (Kolmogorov-Sinai): {final_metric:.4f} bits
Différence observée vs métrique: {abs(final_simple - final_metric):.4f} bits
Différence théorique vs métrique: {abs(evolution[-1].get('simple_entropy_theoretical', 0) - final_metric):.4f} bits

🔬 ANALYSE DE COMPLEXITÉ
"""

        # Ajout des métriques de complexité
        if complexity:
            report += f"""Complexité Lempel-Ziv: {complexity.get('lz_complexity', 'N/A')}
Entropie Topologique: {complexity.get('topological_entropy', 0):.4f} bits
Diversité relative: {complexity.get('sequence_diversity', 0)*100:.1f}%
Taux de répétition: {complexity.get('repetition_rate', 0)*100:.1f}%

🎲 MOTIFS UNIQUES OBSERVÉS
"""
            unique_patterns = complexity.get('unique_patterns', {})
            for length, count in unique_patterns.items():
                report += f"Longueur {length.split('_')[1]}: {count} motifs uniques\n"

        # Initialiser le calculateur, prédicteur, validateur et analyseur différentiel INDEX5
        # CORRECTION: Passer l'instance analyzer pour accéder à _calculate_sequence_entropy_aep
        calculator = INDEX5Calculator(analyzer=self)
        predictor = INDEX5Predictor()
        validator = INDEX5PredictionValidator()
        differential_analyzer = INDEX5DifferentialAnalyzer()

        # Calculer les différentiels
        differentials = differential_analyzer.calculate_differentials(evolution)

        report += f"""
📋 ÉVOLUTION COMPLÈTE - TOUTES LES {len(evolution)} MAINS AVEC MÉTRIQUES ET PRÉDICTIONS INDEX5
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | Uniques | Métriques INDEX5 | Prédiction POUR cette main
---------|-------------|----------|----------------|------|-------------|--------------|---------|------------------|---------------------------
"""

        # Pré-calculer toutes les prédictions pour éviter le décalage
        sequence = analysis_result.get('full_sequence', [])
        predictions = ["N/A"]  # Première main n'a pas de prédiction

        for i in range(len(evolution) - 1):
            item = evolution[i]
            sequence_up_to_i = sequence[:i+1] if i < len(sequence) else sequence
            current_metrics = {
                'conditional_entropy': item.get('conditional_entropy', 0),
                'metric_entropy': item.get('metric_entropy', 0),
                'repetition_rate': item.get('entropy_rate', 0),
                'predictability_score': 1 - (item.get('conditional_entropy', 3.9309) / 3.9309)
            }

            # Calculer la prédiction pour la main suivante (i+1)
            if len(sequence_up_to_i) >= 5:  # Minimum requis pour prédiction
                prediction_result = predictor.predict_next_index5(sequence_up_to_i, current_metrics)
                if prediction_result and isinstance(prediction_result, dict):
                    predicted_value = prediction_result.get('predicted_index5', 'N/A')
                    confidence = prediction_result.get('confidence', 0)

                    # Gestion spéciale pour WAIT
                    if predicted_value == 'WAIT':
                        prediction_display = "WAIT"
                    else:
                        prediction_display = f"{predicted_value}({confidence:.2f})"
                else:
                    # Essayer prédiction contextuelle simple
                    simple_pred = predictor.predict_context_level(sequence_up_to_i, current_metrics)
                    prediction_display = simple_pred if simple_pred else "N/A"
            else:
                prediction_display = "N/A"

            predictions.append(prediction_display)

            # Valider la prédiction avec la valeur réelle suivante
            if prediction_display != "N/A":
                next_actual_index5 = sequence[i + 1]
                validator.validate_prediction(prediction_display, next_actual_index5, item['position'] + 1)

        # Générer le rapport avec les prédictions correctement alignées
        for i, item in enumerate(evolution):
            simple_theo = item.get('simple_entropy_theoretical', 0)
            index5_value = sequence[i] if i < len(sequence) else "N/A"

            # Calculer les métriques INDEX5 pour cette position
            sequence_up_to_i = sequence[:i+1] if i < len(sequence) else sequence
            current_metrics = {
                'conditional_entropy': item.get('conditional_entropy', 0),
                'metric_entropy': item.get('metric_entropy', 0),
                'repetition_rate': item.get('entropy_rate', 0),
                'predictability_score': 1 - (item.get('conditional_entropy', 3.9309) / 3.9309)
            }

            # Calculer les nouvelles métriques INDEX5
            if len(sequence_up_to_i) >= 2:
                index5_metrics = calculator.calculate_all_metrics(sequence_up_to_i, current_metrics, evolution[:i+1])

                # Sélectionner les 3 métriques les plus importantes pour l'affichage
                context_pred = index5_metrics.get('context_predictability', 0)
                pattern_str = index5_metrics.get('pattern_strength', 0)
                consensus = index5_metrics.get('multi_algorithm_consensus', 0)

                metrics_display = f"Ctx:{context_pred:.3f} Pat:{pattern_str:.3f} Con:{consensus:.3f}"
            else:
                metrics_display = "Ctx:0.000 Pat:0.000 Con:0.000"

            # Utiliser la prédiction pré-calculée pour cette main
            prediction_display = predictions[i] if i < len(predictions) else "N/A"

            report += f"Main {item['position']:2d}  | {index5_value:11s} | {item['metric_entropy']:6.3f}  | {item['conditional_entropy']:12.3f}  | {item['entropy_rate']:4.3f} | {item['simple_entropy']:9.3f} | {simple_theo:10.3f} | {item['unique_values']:2d}/18 | {metrics_display} | {prediction_display:14s}\n"

        # Ajouter le nouveau tableau avec les différentiels
        report += f"""

📊 TABLEAU AVEC DIFFÉRENTIELS - ANALYSE DES VARIATIONS ENTRE MAINS
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | DiffCond | DiffTaux | DiffDivEntropG | DiffEntropG | Prédiction
---------|-------------|----------|----------------|------|-------------|--------------|----------|----------|----------------|-------------|------------
"""

        # Générer le tableau avec différentiels
        for i, item in enumerate(evolution):
            simple_theo = item.get('simple_entropy_theoretical', 0)
            index5_value = sequence[i] if i < len(sequence) else "N/A"
            prediction_display = predictions[i] if i < len(predictions) else "N/A"

            # Récupérer les différentiels correspondants
            if i < len(differentials):
                diff_data = differentials[i]
                diff_cond = diff_data.get('diff_conditional', 0)
                diff_taux = diff_data.get('diff_entropy_rate', 0)
                diff_div_entrop = diff_data.get('diff_simple_entropy', 0)
                diff_entrop = diff_data.get('diff_simple_entropy_theoretical', 0)
            else:
                diff_cond = diff_taux = diff_div_entrop = diff_entrop = 0.0

            report += f"Main {item['position']:2d}  | {index5_value:11s} | {item['metric_entropy']:6.3f}  | {item['conditional_entropy']:12.3f}  | {item['entropy_rate']:4.3f} | {item['simple_entropy']:9.3f} | {simple_theo:10.3f} | {diff_cond:6.3f}   | {diff_taux:6.3f}   | {diff_div_entrop:12.3f}   | {diff_entrop:9.3f}   | {prediction_display:14s}\n"

        # Ajouter le nouveau tableau prédictif avec différentiels
        predictive_table_generator = INDEX5PredictiveDifferentialTable()
        predictive_table = predictive_table_generator.generate_predictive_table(
            analysis_result['full_sequence'],
            analysis_result['entropy_evolution'],
            self
        )

        report += f"""

🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS POUR LES 9 VALEURS INDEX5 POSSIBLES
═══════════════════════════════════════════════════════════════════════

{predictive_table}
"""

        # Ajouter les statistiques des différentiels
        diff_stats = differential_analyzer.get_differential_statistics(differentials)
        if diff_stats:
            report += f"""

📈 STATISTIQUES DES DIFFÉRENTIELS
═══════════════════════════════

🔢 DIFFÉRENTIELS ENTROPIE CONDITIONNELLE (DiffCond)
• Minimum: {diff_stats.get('diff_conditional', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_conditional', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_conditional', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_conditional', {}).get('std', 0):.4f} bits

🔢 DIFFÉRENTIELS TAUX D'ENTROPIE (DiffTaux)
• Minimum: {diff_stats.get('diff_entropy_rate', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_entropy_rate', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_entropy_rate', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_entropy_rate', {}).get('std', 0):.4f} bits

🔢 DIFFÉRENTIELS DIVERSITÉ ENTROPIQUE (DiffDivEntropG)
• Minimum: {diff_stats.get('diff_simple_entropy', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_simple_entropy', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_simple_entropy', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_simple_entropy', {}).get('std', 0):.4f} bits

🔢 DIFFÉRENTIELS ENTROPIE GÉNÉRALE (DiffEntropG)
• Minimum: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('std', 0):.4f} bits
"""

        # Ajout d'analyses statistiques détaillées
        report += f"""
📊 ANALYSES STATISTIQUES COMPLÈTES
═══════════════════════════════════

🔢 STATISTIQUES D'ENTROPIE MÉTRIQUE
• Minimum: {min(metric_entropies):.4f} bits (main {metric_entropies.index(min(metric_entropies)) + 1})
• Maximum: {max(metric_entropies):.4f} bits (main {metric_entropies.index(max(metric_entropies)) + 1})
• Écart-type: {np.std(metric_entropies):.4f} bits
• Coefficient de variation: {np.std(metric_entropies)/np.mean(metric_entropies)*100:.1f}%

🔢 STATISTIQUES D'ENTROPIE CONDITIONNELLE
• Minimum: {min(conditional_entropies):.4f} bits (main {conditional_entropies.index(min(conditional_entropies)) + 1})
• Maximum: {max(conditional_entropies):.4f} bits (main {conditional_entropies.index(max(conditional_entropies)) + 1})
• Écart-type: {np.std(conditional_entropies):.4f} bits
• Coefficient de variation: {np.std(conditional_entropies)/np.mean(conditional_entropies)*100:.1f}%

🔢 ÉVOLUTION DE LA DIVERSITÉ
• Diversité initiale: {evolution[0]['unique_values']}/18 ({evolution[0]['unique_values']/18*100:.1f}%)
• Diversité finale: {evolution[-1]['unique_values']}/18 ({evolution[-1]['unique_values']/18*100:.1f}%)
• Croissance de diversité: +{evolution[-1]['unique_values'] - evolution[0]['unique_values']} valeurs uniques

🎯 POINTS D'INTÉRÊT IDENTIFIÉS
• Main avec entropie métrique maximale: {max_metric_pos} ({max(metric_entropies):.4f} bits)
• Main avec entropie conditionnelle maximale: {max_conditional_pos} ({max(conditional_entropies):.4f} bits)
• Stabilisation de l'entropie métrique: {"Oui" if np.std(metric_entropies[-10:]) < 0.05 else "Non"} (10 dernières mains)

🔍 INTERPRÉTATION AVANCÉE
• Entropie métrique moyenne ({np.mean(metric_entropies):.3f} bits) = {np.mean(metric_entropies)/self.theoretical_entropy*100:.1f}% du maximum théorique
• Entropie conditionnelle faible → Forte dépendance temporelle, patterns récurrents exploitables
• Taux d'entropie stable → Information moyenne générée par symbole à long terme
• Complexité LZ ({complexity.get('lz_complexity', 'N/A')}) → Séquence {"hautement" if complexity.get('lz_complexity', 60) < 40 else "modérément"} compressible
• Coefficient de variation faible → Comportement {"stable" if np.std(metric_entropies)/np.mean(metric_entropies) < 0.2 else "variable"}

{validator.get_detailed_report()}
"""

        return report

    def analyze_multiple_games(self, data: List[Dict], max_games: Optional[int] = None) -> Dict:
        """
        Analyse multiple parties et calcule des statistiques globales.

        Args:
            data: Liste des données de parties
            max_games: Nombre maximum de parties à analyser (optionnel)

        Returns:
            Statistiques globales d'analyse
        """
        if max_games:
            data = data[:max_games]

        all_results = []
        successful_analyses = 0

        print(f"🔄 Analyse de {len(data)} parties...")

        for i, game_data in enumerate(data):
            game_id = f"Game_{i+1}"
            result = self.analyze_single_game(game_data, game_id)

            if 'error' not in result:
                all_results.append(result)
                successful_analyses += 1

            if (i + 1) % 10 == 0:
                print(f"   Progression: {i+1}/{len(data)} parties analysées")

        if not all_results:
            return {'error': 'Aucune partie analysée avec succès'}

        # Calcul des statistiques globales
        final_entropies = [result['final_metric_entropy'] for result in all_results]
        sequence_lengths = [result['sequence_length'] for result in all_results]
        max_entropy_positions = [result['max_metric_entropy_position'] for result in all_results]

        return {
            'total_games_analyzed': successful_analyses,
            'average_final_entropy': np.mean(final_entropies),
            'std_final_entropy': np.std(final_entropies),
            'min_final_entropy': np.min(final_entropies),
            'max_final_entropy': np.max(final_entropies),
            'average_sequence_length': np.mean(sequence_lengths),
            'average_max_entropy_position': np.mean(max_entropy_positions),
            'all_results': all_results
        }

    def export_results_to_csv(self, analysis_result: Dict, filename: str):
        """
        Exporte les résultats d'analyse vers un fichier CSV.

        Args:
            analysis_result: Résultats d'analyse d'une partie
            filename: Nom du fichier CSV
        """
        if 'entropy_evolution' not in analysis_result:
            print("❌ Pas de données à exporter")
            return

        evolution = analysis_result['entropy_evolution']

        # Création du DataFrame
        df = pd.DataFrame(evolution)

        # Ajout d'informations sur la partie
        df['game_id'] = analysis_result['game_id']
        df['theoretical_entropy_max'] = self.theoretical_entropy

        # Sauvegarde
        df.to_csv(filename, index=False)
        print(f"📄 Résultats exportés vers: {filename}")


class INDEX5Calculator:
    """
    Classe pour le calcul des métriques entropiques INDEX5
    utilisées comme base pour l'analyse prédictive.
    Chaque méthode calcule une mesure spécifique qui sera ajoutée au rapport.
    """

    def __init__(self, analyzer=None):
        """Initialisation du calculateur INDEX5"""
        # Référence vers l'analyzer pour accéder aux méthodes d'entropie
        self.analyzer = analyzer

        # Probabilités théoriques INDEX5 (adaptées depuis BaccaratEntropyAnalyzer)
        self.THEORETICAL_PROBS = {
            '0_A_BANKER': 0.085136, '1_A_BANKER': 0.086389,
            '0_B_BANKER': 0.064676, '1_B_BANKER': 0.065479,
            '0_C_BANKER': 0.077903, '1_C_BANKER': 0.078929,
            '0_A_PLAYER': 0.085240, '1_A_PLAYER': 0.086361,
            '0_B_PLAYER': 0.076907, '1_B_PLAYER': 0.077888,
            '0_C_PLAYER': 0.059617, '1_C_PLAYER': 0.060352,
            '0_A_TIE': 0.017719, '1_A_TIE': 0.017978,
            '0_B_TIE': 0.016281, '1_B_TIE': 0.016482,
            '0_C_TIE': 0.013241, '1_C_TIE': 0.013423
        }

    def calculate_context_predictability(self, sequence_history, current_metrics):
        """
        Calcule le niveau de prédictibilité contextuelle basé sur l'entropie conditionnelle
        et les patterns récents. Retourne un score de prédictibilité contextuelle.
        """
        if len(sequence_history) < 5:
            return 0.0

        # 1. Score basé sur l'entropie conditionnelle (plus faible = plus prévisible)
        # CORRECTION AEP: Nouvelle normalisation basée sur les vraies plages observées
        conditional_entropy = current_metrics.get('conditional_entropy', 6.2192)
        entropy_score = max(0.0, (6.2192 - conditional_entropy) / 6.2192)  # Normalisation corrigée

        # 2. Score basé sur la répétition des patterns récents
        recent_pattern = sequence_history[-5:]
        pattern_matches = self.count_pattern_occurrences(recent_pattern, sequence_history)
        pattern_score = min(pattern_matches / 10.0, 1.0)  # Normalisation sur 10 occurrences max

        # 3. Score basé sur le taux de répétition
        repetition_rate = current_metrics.get('repetition_rate', 0)
        repetition_score = min(repetition_rate * 5, 1.0)  # Normalisation

        # 4. Score composite pondéré
        context_predictability = (0.5 * entropy_score + 0.3 * pattern_score + 0.2 * repetition_score)

        return round(context_predictability, 4)

    def count_pattern_occurrences(self, pattern, sequence_history):
        """
        Compte le nombre d'occurrences d'un pattern dans l'historique
        """
        if len(pattern) == 0:
            return 0

        pattern_len = len(pattern)
        count = 0

        for i in range(len(sequence_history) - pattern_len + 1):
            if sequence_history[i:i+pattern_len] == pattern:
                count += 1

        return count

    def calculate_pattern_strength(self, sequence_history):
        """
        Calcule la force des patterns récurrents dans la séquence.
        Retourne un score de force des patterns.
        """
        if len(sequence_history) < 4:
            return 0.0

        pattern_strengths = []

        # Analyser patterns de longueur 2 à 5
        for pattern_len in range(2, min(6, len(sequence_history))):
            recent_pattern = sequence_history[-pattern_len:]
            occurrences = self.count_pattern_occurrences(recent_pattern, sequence_history[:-pattern_len])

            if occurrences > 0:
                # Force = occurrences pondérées par la longueur du pattern
                strength = occurrences * (pattern_len / 5.0)
                pattern_strengths.append(strength)

        if pattern_strengths:
            return round(max(pattern_strengths), 4)

        return 0.0

    def calculate_entropy_stability_score(self, entropy_evolution):
        """
        Calcule un score de stabilité de l'entropie métrique.
        Plus le score est élevé, plus l'entropie est stable (système déterministe).
        """
        if len(entropy_evolution) < 5:
            return 0.0

        # Prendre les 10 dernières valeurs d'entropie métrique
        recent_evolution = entropy_evolution[-10:] if len(entropy_evolution) >= 10 else entropy_evolution
        metric_entropies = [item.get('metric_entropy', 0) for item in recent_evolution]

        if len(metric_entropies) < 2:
            return 0.0

        # Calculer la variance (plus faible = plus stable)
        variance = np.var(metric_entropies) if len(metric_entropies) > 0 else 0

        # Convertir en score de stabilité (inverse de la variance, normalisé)
        stability_score = 1.0 / (1.0 + variance * 10)  # Normalisation

        return round(stability_score, 4)

    def calculate_compression_score(self, sequence_history, current_metrics):
        """
        Calcule un score de compressibilité basé sur la complexité LZ.
        Plus le score est élevé, plus la séquence est compressible (patterns répétitifs).
        """
        if len(sequence_history) < 4:
            return 0.0

        # Utiliser la complexité LZ si disponible
        lz_complexity = current_metrics.get('lz_complexity', None)

        if lz_complexity is not None:
            # Normaliser la complexité LZ (plus faible = plus compressible)
            max_complexity = len(sequence_history)  # Complexité maximale théorique
            compression_score = 1.0 - (lz_complexity / max_complexity)
            return round(max(0.0, compression_score), 4)

        # Si pas de complexité LZ, utiliser une approximation basée sur les répétitions
        unique_elements = len(set(sequence_history))
        total_elements = len(sequence_history)

        # Score basé sur la diversité (moins de diversité = plus compressible)
        diversity_ratio = unique_elements / total_elements
        compression_score = 1.0 - diversity_ratio

        return round(compression_score, 4)

    def calculate_structural_richness_score(self, sequence_history, current_metrics):
        """
        Calcule un score de richesse structurelle basé sur l'entropie topologique
        et la diversité des patterns. Plus le score est élevé, plus la structure est riche.
        """
        if len(sequence_history) < 6:
            return 0.0

        # 1. Score basé sur l'entropie topologique si disponible
        topological_entropy = current_metrics.get('topological_entropy', None)
        topo_score = 0.0

        if topological_entropy is not None:
            # Normaliser l'entropie topologique (max théorique ≈ 4.17 pour INDEX5)
            topo_score = min(topological_entropy / 4.17, 1.0)

        # 2. Score basé sur la diversité des patterns de différentes longueurs
        pattern_diversity_scores = []

        for pattern_len in range(2, min(6, len(sequence_history) // 2)):
            patterns_found = set()

            # Extraire tous les patterns de cette longueur
            for i in range(len(sequence_history) - pattern_len + 1):
                pattern = tuple(sequence_history[i:i+pattern_len])
                patterns_found.add(pattern)

            # Score de diversité pour cette longueur
            max_possible_patterns = min(18**pattern_len, len(sequence_history) - pattern_len + 1)
            diversity_score = len(patterns_found) / max_possible_patterns
            pattern_diversity_scores.append(diversity_score)

        # 3. Score composite
        if pattern_diversity_scores:
            avg_diversity = sum(pattern_diversity_scores) / len(pattern_diversity_scores)
            structural_richness = (0.6 * topo_score + 0.4 * avg_diversity)
        else:
            structural_richness = topo_score

        return round(structural_richness, 4)

    def calculate_bayesian_divergence_score(self, sequence_history):
        """
        Calcule la divergence entre les probabilités observées et théoriques INDEX5
        en utilisant la divergence de Kullback-Leibler. Plus le score est élevé,
        plus la séquence s'écarte des probabilités théoriques.
        """
        if len(sequence_history) < 10:
            return 0.0

        from collections import Counter

        # 1. Calculer les fréquences observées
        observed_counts = Counter(sequence_history)
        total_observations = len(sequence_history)

        # 2. Calculer la divergence KL: D_KL(P_obs || P_theo) = Σ P_obs(x) log(P_obs(x) / P_theo(x))
        kl_divergence = 0.0

        for index5_value in self.THEORETICAL_PROBS:
            p_theoretical = self.THEORETICAL_PROBS[index5_value]
            p_observed = observed_counts.get(index5_value, 0) / total_observations

            if p_observed > 0:  # Éviter log(0)
                kl_divergence += p_observed * np.log2(p_observed / p_theoretical)

        # 3. Normaliser le score (la divergence KL peut être très élevée)
        # Utiliser une fonction sigmoïde pour normaliser entre 0 et 1
        normalized_score = 2 / (1 + np.exp(-kl_divergence)) - 1

        return round(max(0.0, normalized_score), 4)

    def calculate_conditional_entropy_context(self, sequence_history, context_length=3):
        """
        Calcule l'entropie conditionnelle pour différents contextes.
        Retourne l'entropie conditionnelle moyenne pour le contexte donné.
        """
        if len(sequence_history) <= context_length:
            return 0.0

        from collections import Counter, defaultdict

        # Analyser les transitions depuis les contextes de longueur donnée
        context_transitions = defaultdict(Counter)

        for i in range(context_length, len(sequence_history)):
            context = tuple(sequence_history[i-context_length:i])
            next_value = sequence_history[i]
            context_transitions[context][next_value] += 1

        # Calculer l'entropie conditionnelle H(X|Context)
        total_transitions = sum(sum(transitions.values()) for transitions in context_transitions.values())

        if total_transitions == 0:
            return 0.0

        conditional_entropy = 0.0

        for context, transitions in context_transitions.items():
            context_total = sum(transitions.values())
            context_prob = context_total / total_transitions

            # CORRECTION AEP: Entropie pour ce contexte spécifique selon AEP
            # Créer la séquence des valeurs suivantes pour ce contexte
            context_sequence = []
            for next_value, count in transitions.items():
                context_sequence.extend([next_value] * count)

            # Appeler la méthode depuis l'analyzer
            if self.analyzer:
                context_entropy = self.analyzer._calculate_sequence_entropy_aep(context_sequence)
            else:
                # Fallback si pas d'analyzer (ne devrait pas arriver)
                context_entropy = 0.0

            # Pondérer par la probabilité du contexte
            conditional_entropy += context_prob * context_entropy

        return round(conditional_entropy, 4)

    def calculate_multi_algorithm_consensus_score(self, sequence_history, all_metrics):
        """
        Calcule un score de consensus entre différents algorithmes d'analyse.
        Plus le score est élevé, plus les différentes méthodes sont en accord
        sur les caractéristiques de la séquence.
        """
        if not sequence_history or len(sequence_history) < 5:
            return 0.0

        # 1. Calculer différents scores avec les méthodes disponibles
        scores = {}

        # Score de prédictibilité contextuelle
        context_score = self.calculate_context_predictability(sequence_history, all_metrics)
        scores['context'] = context_score

        # Score de force des patterns
        pattern_score = self.calculate_pattern_strength(sequence_history)
        scores['pattern'] = pattern_score

        # Score de compression
        compression_score = self.calculate_compression_score(sequence_history, all_metrics)
        scores['compression'] = compression_score

        # Score de divergence bayésienne (inverser pour cohérence)
        divergence_score = self.calculate_bayesian_divergence_score(sequence_history)
        scores['bayesian'] = 1.0 - divergence_score

        # 2. Calculer la variance des scores (faible variance = consensus élevé)
        score_values = list(scores.values())
        if len(score_values) < 2:
            return 0.0

        mean_score = sum(score_values) / len(score_values)
        variance = sum((score - mean_score) ** 2 for score in score_values) / len(score_values)

        # 3. Convertir en score de consensus (faible variance = consensus élevé)
        consensus_score = 1.0 / (1.0 + variance * 10)  # Normalisation

        return round(consensus_score, 4)

    def calculate_deterministic_pattern_score(self, sequence_history):
        """
        Calcule un score de déterminisme basé sur la récurrence des patterns.
        Plus le score est élevé, plus la séquence présente des patterns déterministes.
        """
        if len(sequence_history) < 4:
            return 0.0

        pattern_scores = []

        # Analyser patterns de longueur 2 à 5
        for pattern_length in range(2, min(6, len(sequence_history))):
            current_pattern = sequence_history[-pattern_length:]

            # Chercher ce pattern dans l'historique (excluant la fin)
            continuations = self.find_pattern_continuations(current_pattern, sequence_history[:-pattern_length])

            if continuations:
                # Calculer la prévisibilité de ce pattern
                total_continuations = sum(continuations.values())
                max_continuation = max(continuations.values())

                # Score = (fréquence max / total) * poids de longueur
                pattern_predictability = max_continuation / total_continuations
                length_weight = pattern_length / 5.0  # Normalisation

                pattern_score = pattern_predictability * length_weight
                pattern_scores.append(pattern_score)

        if pattern_scores:
            # Retourner le score maximum (meilleur pattern déterministe)
            return round(max(pattern_scores), 4)

        return 0.0

    def find_pattern_continuations(self, pattern, sequence_history):
        """
        Trouve toutes les continuations d'un pattern dans l'historique
        """
        continuations = {}
        pattern_len = len(pattern)

        for i in range(len(sequence_history) - pattern_len):
            if sequence_history[i:i+pattern_len] == pattern:
                # Si il y a une continuation après ce pattern
                if i + pattern_len < len(sequence_history):
                    next_value = sequence_history[i + pattern_len]
                    continuations[next_value] = continuations.get(next_value, 0) + 1

        return continuations

    def calculate_bayesian_theoretical_alignment(self, sequence_history):
        """
        Calcule l'alignement entre les observations récentes et les probabilités théoriques
        en utilisant une approche bayésienne. Plus le score est élevé, plus les observations
        récentes sont alignées avec la théorie.
        """
        if len(sequence_history) < 10:
            return 0.0

        from collections import Counter

        # Analyser les 20 dernières observations
        recent_sequence = sequence_history[-20:] if len(sequence_history) >= 20 else sequence_history
        observed_freq = Counter(recent_sequence)

        # Calculer le score d'alignement bayésien
        alignment_scores = []

        for index5_value in self.THEORETICAL_PROBS.keys():
            p_theoretical = self.THEORETICAL_PROBS[index5_value]
            observed_count = observed_freq.get(index5_value, 0)
            p_observed = observed_count / len(recent_sequence)

            # Score d'alignement pour cette valeur (1 - différence relative)
            if p_theoretical > 0:
                relative_diff = abs(p_observed - p_theoretical) / p_theoretical
                alignment_score = max(0.0, 1.0 - relative_diff)
                alignment_scores.append(alignment_score)

        if alignment_scores:
            # Score moyen pondéré par les probabilités théoriques
            weighted_alignment = 0.0
            total_weight = 0.0

            for i, (index5_value, p_theoretical) in enumerate(self.THEORETICAL_PROBS.items()):
                if i < len(alignment_scores):
                    weighted_alignment += alignment_scores[i] * p_theoretical
                    total_weight += p_theoretical

            if total_weight > 0:
                return round(weighted_alignment / total_weight, 4)

        return 0.0

    def calculate_transition_matrix_entropy(self, sequence_history):
        """
        Calcule l'entropie de la matrice de transitions INDEX5.
        Plus l'entropie est faible, plus les transitions sont prévisibles.
        """
        if len(sequence_history) < 3:
            return 0.0

        from collections import Counter, defaultdict

        # Construire la matrice de transitions
        transitions = defaultdict(Counter)

        for i in range(len(sequence_history) - 1):
            current = sequence_history[i]
            next_val = sequence_history[i + 1]
            transitions[current][next_val] += 1

        # Calculer l'entropie de chaque ligne de la matrice
        total_entropy = 0.0
        total_weight = 0.0

        for current_state, next_states in transitions.items():
            state_total = sum(next_states.values())
            state_weight = state_total / (len(sequence_history) - 1)

            # CORRECTION AEP: Entropie pour cet état selon AEP
            # Créer la séquence des états suivants pour cet état
            state_sequence = []
            for next_state, count in next_states.items():
                state_sequence.extend([next_state] * count)

            # Appeler la méthode depuis l'analyzer
            if self.analyzer:
                state_entropy = self.analyzer._calculate_sequence_entropy_aep(state_sequence)
            else:
                # Fallback si pas d'analyzer (ne devrait pas arriver)
                state_entropy = 0.0

            total_entropy += state_entropy * state_weight
            total_weight += state_weight

        if total_weight > 0:
            return round(total_entropy / total_weight, 4)

        return 0.0

    def calculate_frequency_stability_score(self, sequence_history):
        """
        Calcule un score de stabilité des fréquences INDEX5.
        Compare les fréquences récentes avec les fréquences globales.
        Plus le score est élevé, plus les fréquences sont stables.
        """
        if len(sequence_history) < 20:
            return 0.0

        from collections import Counter

        # Fréquences globales
        global_freq = Counter(sequence_history)
        global_total = len(sequence_history)

        # Fréquences récentes (30 dernières observations)
        recent_sequence = sequence_history[-30:] if len(sequence_history) >= 30 else sequence_history[-len(sequence_history)//2:]
        recent_freq = Counter(recent_sequence)
        recent_total = len(recent_sequence)

        # Calculer la stabilité pour chaque valeur INDEX5
        stability_scores = []

        for index5_value in self.THEORETICAL_PROBS.keys():
            global_prob = global_freq.get(index5_value, 0) / global_total
            recent_prob = recent_freq.get(index5_value, 0) / recent_total

            # Score de stabilité = 1 - différence relative
            if global_prob > 0:
                relative_diff = abs(recent_prob - global_prob) / global_prob
                stability_score = max(0.0, 1.0 - relative_diff)
                stability_scores.append(stability_score)

        if stability_scores:
            return round(sum(stability_scores) / len(stability_scores), 4)

        return 0.0

    def calculate_all_metrics(self, sequence_history, current_metrics, entropy_evolution):
        """
        Calcule toutes les métriques disponibles pour une position donnée.
        Retourne un dictionnaire avec tous les scores calculés.
        """
        if len(sequence_history) < 2:
            return {}

        metrics = {}

        # 1. Prédictibilité contextuelle
        metrics['context_predictability'] = self.calculate_context_predictability(sequence_history, current_metrics)

        # 2. Force des patterns
        metrics['pattern_strength'] = self.calculate_pattern_strength(sequence_history)

        # 3. Score de stabilité entropique
        if entropy_evolution:
            metrics['entropy_stability'] = self.calculate_entropy_stability_score(entropy_evolution)

        # 4. Score de compression
        metrics['compression_score'] = self.calculate_compression_score(sequence_history, current_metrics)

        # 5. Richesse structurelle
        metrics['structural_richness'] = self.calculate_structural_richness_score(sequence_history, current_metrics)

        # 6. Divergence bayésienne
        metrics['bayesian_divergence'] = self.calculate_bayesian_divergence_score(sequence_history)

        # 7. Entropie conditionnelle contextuelle
        metrics['conditional_entropy_context'] = self.calculate_conditional_entropy_context(sequence_history)

        # 8. Consensus multi-algorithmes
        metrics['multi_algorithm_consensus'] = self.calculate_multi_algorithm_consensus_score(sequence_history, current_metrics)

        # 9. Score de patterns déterministes
        metrics['deterministic_pattern_score'] = self.calculate_deterministic_pattern_score(sequence_history)

        # 10. Alignement bayésien théorique
        metrics['bayesian_theoretical_alignment'] = self.calculate_bayesian_theoretical_alignment(sequence_history)

        # 11. Entropie de la matrice de transitions
        metrics['transition_matrix_entropy'] = self.calculate_transition_matrix_entropy(sequence_history)

        # 12. Stabilité des fréquences
        metrics['frequency_stability'] = self.calculate_frequency_stability_score(sequence_history)

        return metrics


class INDEX5Predictor:
    """
    Classe pour les prédictions INDEX5 basées sur les métriques calculées
    par INDEX5Calculator. Cette classe utilise les mesures entropiques
    pour effectuer des prédictions de la prochaine valeur INDEX5.
    """

    def __init__(self):
        """Initialisation du prédicteur INDEX5"""
        # Probabilités théoriques INDEX5
        self.THEORETICAL_PROBS = {
            '0_A_BANKER': 0.085136, '1_A_BANKER': 0.086389,
            '0_B_BANKER': 0.064676, '1_B_BANKER': 0.065479,
            '0_C_BANKER': 0.077903, '1_C_BANKER': 0.078929,
            '0_A_PLAYER': 0.085240, '1_A_PLAYER': 0.086361,
            '0_B_PLAYER': 0.076907, '1_B_PLAYER': 0.077888,
            '0_C_PLAYER': 0.059617, '1_C_PLAYER': 0.060352,
            '0_A_TIE': 0.017719, '1_A_TIE': 0.017978,
            '0_B_TIE': 0.016281, '1_B_TIE': 0.016482,
            '0_C_TIE': 0.013241, '1_C_TIE': 0.013423
        }

    def predict_context_level(self, sequence_history, current_metrics):
        """
        Analyse contextuelle temporelle pour prédiction INDEX5
        Utilise entropie conditionnelle et taux de répétition
        """
        if len(sequence_history) < 5:
            return None

        # 1. Analyser les 5-10 dernières mains pour patterns courts
        recent_pattern = sequence_history[-5:]

        # 2. Si entropie conditionnelle < 3.8 bits → Forte prédictibilité
        # CORRECTION AEP: Nouveau seuil basé sur les vraies valeurs observées (minimum ~3.7 bits)
        if current_metrics.get('conditional_entropy', 6.2192) < 3.8:
            # Chercher pattern exact dans l'historique
            return self.find_exact_pattern_continuation(recent_pattern, sequence_history)

        # 3. Si taux répétition > 15% → Tendance répétitive
        if current_metrics.get('repetition_rate', 0) > 0.15:
            return self.predict_repetition_bias(sequence_history[-1])

        return None

    def find_exact_pattern_continuation(self, pattern, sequence_history):
        """
        Trouve les continuations d'un pattern exact dans l'historique
        """
        continuations = {}
        pattern_len = len(pattern)

        for i in range(len(sequence_history) - pattern_len):
            if sequence_history[i:i+pattern_len] == pattern:
                # Si il y a une continuation après ce pattern
                if i + pattern_len < len(sequence_history):
                    next_value = sequence_history[i + pattern_len]
                    continuations[next_value] = continuations.get(next_value, 0) + 1

        if continuations:
            # Retourner la continuation la plus fréquente
            best_continuation = max(continuations.items(), key=lambda x: x[1])
            return best_continuation[0]

        return None

    def predict_repetition_bias(self, last_value):
        """
        Prédit une répétition de la dernière valeur
        """
        return last_value

    def predict_entropy_level(self, sequence_history, entropy_evolution):
        """
        Prédiction basée sur l'analyse entropique avancée
        Utilise entropie métrique, complexité LZ, entropie topologique
        """
        if not entropy_evolution:
            return None

        current_metrics = entropy_evolution[-1] if entropy_evolution else {}

        # 1. Si entropie métrique stable → Système déterministe
        if self.is_metric_entropy_stable(entropy_evolution[-10:]):
            # Utiliser modèle déterministe basé sur transitions
            return self.predict_deterministic_model(sequence_history)

        # 2. Si complexité LZ faible → Séquence compressible
        if current_metrics.get('lz_complexity', 100) < 35:
            # Exploiter patterns de compression
            return self.predict_compression_patterns(sequence_history)

        # 3. Si entropie topologique élevée → Richesse structurelle
        # CORRECTION AEP: Nouveau seuil basé sur les valeurs observées (3.8-4.1 bits)
        if current_metrics.get('topological_entropy', 0) > 4.05:
            # Modèle sophistiqué multi-patterns
            return self.predict_rich_structure_model(sequence_history)

        return None

    def is_metric_entropy_stable(self, recent_entropy_evolution):
        """
        Vérifie si l'entropie métrique est stable
        """
        if len(recent_entropy_evolution) < 5:
            return False

        metric_entropies = [item.get('metric_entropy', 0) for item in recent_entropy_evolution]
        variance = np.var(metric_entropies) if len(metric_entropies) > 0 else 0

        return variance < 0.1  # Seuil de stabilité

    def predict_deterministic_model(self, sequence_history):
        """
        Modèle déterministe basé sur les transitions
        """
        return self.predict_transition_analysis(sequence_history, {})

    def predict_compression_patterns(self, sequence_history):
        """
        Exploite les patterns de compression pour prédiction
        """
        # Chercher les patterns répétitifs les plus récents
        for pattern_len in range(2, min(6, len(sequence_history))):
            recent_pattern = sequence_history[-pattern_len:]

            # Chercher ce pattern dans l'historique
            continuation = self.find_exact_pattern_continuation(recent_pattern, sequence_history)
            if continuation:
                return continuation

        return None

    def predict_rich_structure_model(self, sequence_history):
        """
        Modèle sophistiqué pour structures riches
        """
        # Combiner plusieurs approches pour structures complexes
        predictions = []

        # Analyse de transitions
        trans_pred = self.predict_transition_analysis(sequence_history, {})
        if trans_pred:
            predictions.append(trans_pred)

        # Analyse de patterns
        pattern_pred = self.predict_compression_patterns(sequence_history)
        if pattern_pred:
            predictions.append(pattern_pred)

        # Retourner la prédiction la plus fréquente
        if predictions:
            from collections import Counter
            counter = Counter(predictions)
            return counter.most_common(1)[0][0]

        return None

    def predict_bayesian_level(self, sequence_history, observed_frequencies):
        """
        Prédiction bayésienne utilisant probabilités théoriques INDEX5
        """
        # 1. Calculer probabilités conditionnelles observées
        conditional_probs = self.calculate_conditional_probabilities(sequence_history)

        # 2. Pondérer avec probabilités théoriques (Bayes)
        bayesian_probs = {}
        for index5_value in self.THEORETICAL_PROBS:
            # P(Xₙ₊₁|contexte) ∝ P(contexte|Xₙ₊₁) × P(Xₙ₊₁)
            observed_prob = conditional_probs.get(index5_value, 0)
            theoretical_prob = self.THEORETICAL_PROBS[index5_value]

            bayesian_prob = observed_prob * theoretical_prob
            bayesian_probs[index5_value] = bayesian_prob

        # Normaliser les probabilités
        total_prob = sum(bayesian_probs.values())
        if total_prob > 0:
            normalized_probs = {k: v/total_prob for k, v in bayesian_probs.items()}

            # Retourner la valeur avec la plus haute probabilité
            best_prediction = max(normalized_probs.items(), key=lambda x: x[1])
            return best_prediction[0]

        return None

    def calculate_conditional_probabilities(self, sequence_history):
        """
        Calcule les probabilités conditionnelles observées
        """
        from collections import Counter, defaultdict

        # Analyser les transitions depuis les 3 dernières valeurs
        context_transitions = defaultdict(Counter)

        for i in range(3, len(sequence_history)):
            context = tuple(sequence_history[i-3:i])
            next_value = sequence_history[i]
            context_transitions[context][next_value] += 1

        # Calculer probabilités conditionnelles pour le contexte actuel
        if len(sequence_history) >= 3:
            current_context = tuple(sequence_history[-3:])

            if current_context in context_transitions:
                transitions = context_transitions[current_context]
                total_transitions = sum(transitions.values())

                conditional_probs = {
                    value: count / total_transitions
                    for value, count in transitions.items()
                }

                return conditional_probs

        return {}

    def apply_index1_constraint(self, current_index5, predicted_index5):
        """
        Applique la contrainte INDEX1 déterministe à la prédiction
        Règles INDEX1:
        - Si INDEX2=C: INDEX1 s'inverse (0→1, 1→0)
        - Si INDEX2=A ou B: INDEX1 se conserve (0→0, 1→1)
        """
        if not current_index5 or not predicted_index5:
            return predicted_index5

        try:
            # Extraire INDEX1 et INDEX2 actuels
            current_parts = current_index5.split('_')
            current_index1 = int(current_parts[0])
            current_index2 = current_parts[1]

            # Calculer INDEX1 obligatoire pour n+1 selon les règles déterministes
            if current_index2 == 'C':
                required_index1 = 1 - current_index1  # Inversion obligatoire
            else:  # A ou B
                required_index1 = current_index1      # Conservation obligatoire

            # Extraire INDEX2 et INDEX3 de la prédiction
            predicted_parts = predicted_index5.split('_')
            if len(predicted_parts) >= 3:
                predicted_index2 = predicted_parts[1]
                predicted_index3 = predicted_parts[2]

                # Construire INDEX5 contraint avec INDEX1 déterministe
                constrained_index5 = f"{required_index1}_{predicted_index2}_{predicted_index3}"
                return constrained_index5

        except (IndexError, ValueError):
            # En cas d'erreur, retourner la prédiction originale
            pass

        return predicted_index5



    def calculate_required_index1(self, current_index5):
        """
        Calcule INDEX1 obligatoire selon les règles déterministes
        Règles INDEX1:
        - Si INDEX2=C: INDEX1 s'inverse (0→1, 1→0)
        - Si INDEX2=A ou B: INDEX1 se conserve (0→0, 1→1)
        """
        if not current_index5:
            return None

        try:
            current_parts = current_index5.split('_')
            current_index1 = int(current_parts[0])
            current_index2 = current_parts[1]

            if current_index2 == 'C':
                return 1 - current_index1  # Inversion obligatoire
            else:  # A ou B
                return current_index1      # Conservation obligatoire
        except:
            return None

    def get_valid_index5_values(self, required_index1):
        """
        Retourne tous les INDEX5 avec INDEX1 obligatoire
        """
        if required_index1 is None:
            return []

        valid_values = []
        for index2 in ['A', 'B', 'C']:
            for index3 in ['BANKER', 'PLAYER', 'TIE']:
                valid_values.append(f"{required_index1}_{index2}_{index3}")
        return valid_values

    def filter_prediction_by_constraint(self, prediction, valid_values):
        """
        Filtre une prédiction selon les contraintes INDEX1
        Retourne la prédiction si valide, None sinon
        """
        if prediction and prediction in valid_values:
            return prediction
        return None

    def predict_next_index5(self, sequence_history, all_metrics):
        """
        Prédicteur INDEX5 principal utilisant fusion multi-algorithmes
        AVEC CONTRAINTE INDEX1 DÉTERMINISTE APPLIQUÉE AVANT LE VOTE
        """
        if not sequence_history or not all_metrics:
            return None

        # ÉTAPE 1: Déterminer INDEX1 obligatoire selon les règles déterministes
        current_index5 = sequence_history[-1]
        required_index1 = self.calculate_required_index1(current_index5)

        if required_index1 is None:
            return None

        # ÉTAPE 2: Obtenir la liste des INDEX5 valides
        valid_index5_values = self.get_valid_index5_values(required_index1)

        # ÉTAPE 3: Évaluation de la prédictibilité actuelle
        conditional_entropy = all_metrics.get('conditional_entropy', 6.2192)
        current_predictability = max(0.0, (6.2192 - conditional_entropy) / 6.2192)

        # ÉTAPE 4: Sélection de la stratégie optimale
        if current_predictability > 0.40:  # Très prévisible
            weight_deterministic = 0.7
            weight_bayesian = 0.2
            weight_frequency = 0.1
        elif current_predictability > 0.30:  # Prévisible
            weight_deterministic = 0.5
            weight_bayesian = 0.3
            weight_frequency = 0.2
        else:  # Moins prévisible
            weight_deterministic = 0.3
            weight_bayesian = 0.5
            weight_frequency = 0.2

        # ÉTAPE 5: Calcul des prédictions FILTRÉES par chaque méthode
        pred_deterministic_raw = self.predict_deterministic_patterns(sequence_history, all_metrics)
        pred_bayesian_raw = self.predict_bayesian_theoretical(sequence_history, all_metrics)
        pred_frequency_raw = self.predict_frequency_based(sequence_history, all_metrics)

        # FILTRAGE : Ne garder que les prédictions qui respectent INDEX1
        pred_deterministic = self.filter_prediction_by_constraint(pred_deterministic_raw, valid_index5_values)
        pred_bayesian = self.filter_prediction_by_constraint(pred_bayesian_raw, valid_index5_values)
        pred_frequency = self.filter_prediction_by_constraint(pred_frequency_raw, valid_index5_values)

        # ÉTAPE 6: Fusion pondérée des prédictions VALIDES uniquement
        predictions = []
        if pred_deterministic:
            predictions.append(('DETERMINISTIC', pred_deterministic, weight_deterministic))
        if pred_bayesian:
            predictions.append(('BAYESIAN', pred_bayesian, weight_bayesian))
        if pred_frequency:
            predictions.append(('FREQUENCY', pred_frequency, weight_frequency))

        # ÉTAPE 7: Si aucune prédiction valide → WAIT
        if not predictions:
            return {
                'predicted_index5': 'WAIT',
                'confidence': 0.0,
                'predictability_score': current_predictability,
                'contributing_methods': [],
                'constraint_applied': True,
                'original_prediction': 'WAIT',
                'reason': f'Aucune prédiction valide pour INDEX1={required_index1}'
            }

        # ÉTAPE 8: Vote pondéré sur les prédictions valides
        vote_weights = {}
        for method, pred, weight in predictions:
            vote_weights[pred] = vote_weights.get(pred, 0) + weight

        # Retourner la prédiction avec le plus fort poids
        best_prediction = max(vote_weights.items(), key=lambda x: x[1])
        final_prediction = best_prediction[0]

        # RESTAURATION: Utiliser le poids pondéré cumulé comme dans l'ancien code
        weighted_confidence = best_prediction[1]

        return {
            'predicted_index5': final_prediction,
            'confidence': weighted_confidence,
            'predictability_score': current_predictability,
            'contributing_methods': [p[0] for p in predictions if p[1] == final_prediction],
            'constraint_applied': True,
            'original_prediction': final_prediction,
            'required_index1': required_index1
        }

    def predict_deterministic_patterns(self, sequence_history, metrics):
        """
        Exploite les patterns récurrents détectés
        """
        pattern_predictions = {}

        for pattern_length in range(2, 6):
            if len(sequence_history) >= pattern_length:
                current_pattern = sequence_history[-pattern_length:]

                # Chercher ce pattern dans l'historique
                continuations = self.find_pattern_continuations(current_pattern, sequence_history)

                if continuations:
                    # Pondérer par fréquence et récence
                    for continuation, freq in continuations.items():
                        weight = freq * (1.0 / pattern_length)  # Patterns courts = plus fiables
                        pattern_predictions[continuation] = pattern_predictions.get(continuation, 0) + weight

        if pattern_predictions:
            # Normaliser et retourner le meilleur
            total_weight = sum(pattern_predictions.values())
            normalized = {k: v/total_weight for k, v in pattern_predictions.items()}
            return max(normalized.items(), key=lambda x: x[1])[0]

        return None

    def find_pattern_continuations(self, pattern, sequence_history):
        """
        Trouve toutes les continuations d'un pattern dans l'historique
        """
        continuations = {}
        pattern_len = len(pattern)

        for i in range(len(sequence_history) - pattern_len):
            if sequence_history[i:i+pattern_len] == pattern:
                # Si il y a une continuation après ce pattern
                if i + pattern_len < len(sequence_history):
                    next_value = sequence_history[i + pattern_len]
                    continuations[next_value] = continuations.get(next_value, 0) + 1

        return continuations

    def predict_bayesian_theoretical(self, sequence_history, metrics):
        """
        Combine observations avec probabilités théoriques INDEX5
        """
        from collections import Counter

        # Calculer fréquences observées récentes (20 dernières mains)
        recent_sequence = sequence_history[-20:] if len(sequence_history) >= 20 else sequence_history
        observed_freq = Counter(recent_sequence)

        bayesian_probs = {}

        for index5_value in self.THEORETICAL_PROBS.keys():
            # Probabilité théorique
            p_theoretical = self.THEORETICAL_PROBS[index5_value]

            # Probabilité observée (avec lissage de Laplace)
            observed_count = observed_freq.get(index5_value, 0)
            p_observed = (observed_count + 1) / (len(recent_sequence) + len(self.THEORETICAL_PROBS))

            # Fusion Bayésienne (pondération adaptative selon la prédictibilité)
            predictability = metrics.get('predictability_score', 0.5)

            # Plus c'est prévisible, plus on fait confiance aux observations
            bayesian_prob = (predictability * p_observed +
                            (1 - predictability) * p_theoretical)

            bayesian_probs[index5_value] = bayesian_prob

        if bayesian_probs:
            return max(bayesian_probs.items(), key=lambda x: x[1])[0]

        return None

    def predict_transition_analysis(self, sequence_history, metrics):
        """
        Analyse les transitions conditionnelles INDEX5
        """
        from collections import Counter

        # Construire matrice de transitions
        transitions = {}

        for i in range(len(sequence_history) - 1):
            current = sequence_history[i]
            next_val = sequence_history[i + 1]

            if current not in transitions:
                transitions[current] = Counter()
            transitions[current][next_val] += 1

        # Prédire basé sur la dernière valeur
        if sequence_history:
            last_value = sequence_history[-1]

            if last_value in transitions:
                # Normaliser les transitions depuis cette valeur
                total_transitions = sum(transitions[last_value].values())
                transition_probs = {
                    next_val: count / total_transitions
                    for next_val, count in transitions[last_value].items()
                }

                if transition_probs:
                    return max(transition_probs.items(), key=lambda x: x[1])[0]

        return None

    def predict_frequency_based(self, sequence_history, metrics):
        """
        Prédiction basée sur les fréquences observées
        """
        from collections import Counter

        # Analyser les fréquences récentes
        recent_sequence = sequence_history[-30:] if len(sequence_history) >= 30 else sequence_history
        freq_counter = Counter(recent_sequence)

        if freq_counter:
            # Retourner la valeur la plus fréquente récemment
            return freq_counter.most_common(1)[0][0]

        return None


class INDEX5DifferentialAnalyzer:
    """
    Classe pour analyser les différentiels (variations) entre les mains
    des métriques entropiques principales.

    Calcule les différences absolues entre mains consécutives pour :
    - DiffCond : |Conditionnelle(n) - Conditionnelle(n-1)|
    - DiffTaux : |Taux(n) - Taux(n-1)|
    - DiffDivEntropG : |DivEntropG(n) - DivEntropG(n-1)|
    - DiffEntropG : |EntropG(n) - EntropG(n-1)|
    """

    def __init__(self):
        pass

    def calculate_differentials(self, entropy_evolution):
        """
        Calcule les différentiels pour toutes les métriques.

        Args:
            entropy_evolution: Liste des résultats d'évolution entropique

        Returns:
            Liste des différentiels pour chaque main
        """
        if not entropy_evolution or len(entropy_evolution) < 2:
            return []

        differentials = []

        # Première main : différentiels = 0 (pas de main précédente)
        differentials.append({
            'position': 1,
            'diff_conditional': 0.0,
            'diff_entropy_rate': 0.0,
            'diff_simple_entropy': 0.0,
            'diff_simple_entropy_theoretical': 0.0
        })

        # Calculer les différentiels pour les mains suivantes
        for i in range(1, len(entropy_evolution)):
            current = entropy_evolution[i]
            previous = entropy_evolution[i-1]

            diff_conditional = abs(current.get('conditional_entropy', 0) - previous.get('conditional_entropy', 0))
            diff_entropy_rate = abs(current.get('entropy_rate', 0) - previous.get('entropy_rate', 0))
            diff_simple_entropy = abs(current.get('simple_entropy', 0) - previous.get('simple_entropy', 0))
            diff_simple_entropy_theoretical = abs(current.get('simple_entropy_theoretical', 0) - previous.get('simple_entropy_theoretical', 0))

            differentials.append({
                'position': current.get('position', i+1),
                'diff_conditional': diff_conditional,
                'diff_entropy_rate': diff_entropy_rate,
                'diff_simple_entropy': diff_simple_entropy,
                'diff_simple_entropy_theoretical': diff_simple_entropy_theoretical
            })

        return differentials

    def get_differential_statistics(self, differentials):
        """
        Calcule les statistiques sur les différentiels.

        Args:
            differentials: Liste des différentiels calculés

        Returns:
            Dictionnaire avec les statistiques
        """
        if not differentials or len(differentials) < 2:
            return {}

        # Exclure la première main (différentiels = 0)
        valid_diffs = differentials[1:]

        stats = {}

        for metric in ['diff_conditional', 'diff_entropy_rate', 'diff_simple_entropy', 'diff_simple_entropy_theoretical']:
            values = [d[metric] for d in valid_diffs]

            if values:
                stats[metric] = {
                    'min': min(values),
                    'max': max(values),
                    'mean': sum(values) / len(values),
                    'std': (sum((x - sum(values)/len(values))**2 for x in values) / len(values))**0.5 if len(values) > 1 else 0.0
                }

        return stats


class INDEX5PredictiveDifferentialTable:
    """
    Nouvelle classe pour générer le tableau prédictif avec différentiels
    pour les 9 valeurs INDEX5 possibles selon les règles INDEX1 déterministes
    """

    def __init__(self):
        """Initialisation de la classe tableau prédictif"""
        self.all_index5_values = [
            "0_A_BANKER", "0_B_BANKER", "0_C_BANKER",
            "0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER",
            "0_A_TIE", "0_B_TIE", "0_C_TIE",
            "1_A_BANKER", "1_B_BANKER", "1_C_BANKER",
            "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER",
            "1_A_TIE", "1_B_TIE", "1_C_TIE"
        ]

    def calculate_required_index1(self, current_index5):
        """
        Calcule INDEX1 obligatoire selon les règles déterministes

        Règles:
        - Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
        - Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
        """
        if not current_index5:
            return None

        try:
            parts = current_index5.split('_')
            current_index1 = int(parts[0])
            current_index2 = parts[1]

            if current_index2 == 'C':
                return 1 - current_index1  # Inversion
            else:  # A ou B
                return current_index1      # Conservation
        except:
            return None

    def get_valid_index5_values(self, required_index1):
        """
        Retourne les 9 valeurs INDEX5 possibles avec INDEX1 obligatoire
        """
        if required_index1 is None:
            return []

        valid_values = []
        for index2 in ['A', 'B', 'C']:
            for index3 in ['BANKER', 'PLAYER', 'TIE']:
                valid_values.append(f"{required_index1}_{index2}_{index3}")
        return valid_values

    def calculate_simulated_metrics(self, sequence, position, possible_index5, analyzer):
        """
        Calcule les métriques pour une séquence simulée avec possible_index5 ajouté
        """
        if position >= len(sequence):
            return {}

        # Créer séquence simulée avec la valeur possible ajoutée
        simulated_sequence = sequence[:position+1] + [possible_index5]

        # Calculer les métriques de base pour la nouvelle position
        metrics = {}

        try:
            # Entropie conditionnelle
            if len(simulated_sequence) >= 2:
                metrics['conditional_entropy'] = analyzer._calculate_conditional_entropy(simulated_sequence)
            else:
                metrics['conditional_entropy'] = 0.0

            # Entropie simple (Shannon)
            metrics['simple_entropy'] = analyzer._calculate_shannon_entropy(simulated_sequence)

            # Entropie théorique (AEP)
            metrics['simple_entropy_theoretical'] = analyzer._calculate_sequence_entropy_aep(simulated_sequence)

            # Taux d'entropie (approximation)
            if len(simulated_sequence) >= 3:
                block_entropies = analyzer._calculate_block_entropies(simulated_sequence, min(5, len(simulated_sequence)))
                if block_entropies:
                    metrics['entropy_rate'] = block_entropies[-1]
                else:
                    metrics['entropy_rate'] = metrics['simple_entropy']
            else:
                metrics['entropy_rate'] = metrics['simple_entropy']

        except Exception as e:
            # En cas d'erreur, retourner des valeurs par défaut
            metrics = {
                'conditional_entropy': 0.0,
                'simple_entropy': 0.0,
                'simple_entropy_theoretical': 0.0,
                'entropy_rate': 0.0
            }

        return metrics

    def calculate_predictive_differentials(self, sequence, evolution, position, analyzer):
        """
        Calcule les différentiels prédictifs pour les 9 valeurs INDEX5 possibles à la main n+1

        ALGORITHME CORRIGÉ :
        1. À la main n : Récupérer les métriques actuelles (Conditionnelle, Taux, DivEntropG, EntropG)
        2. Pour chacune des 9 valeurs INDEX5 possibles à n+1 (selon règles INDEX1) :
           - Simuler l'ajout de cette valeur à la séquence
           - Calculer les nouvelles métriques pour la main n+1 simulée
           - Calculer |métrique(n+1) - métrique(n)| pour chaque métrique
        """
        if position >= len(sequence) or position >= len(evolution):
            return {}

        # ÉTAPE 1: Obtenir l'INDEX5 actuel et ses métriques à la position n
        current_index5 = sequence[position]
        current_metrics = evolution[position]

        # Métriques actuelles à la main n
        current_conditional = current_metrics.get('conditional_entropy', 0.0)
        current_rate = current_metrics.get('entropy_rate', 0.0)
        current_simple = current_metrics.get('simple_entropy', 0.0)
        current_theoretical = current_metrics.get('simple_entropy_theoretical', 0.0)

        # ÉTAPE 2: Calculer INDEX1 requis pour la main n+1 selon les règles déterministes
        required_index1 = self.calculate_required_index1(current_index5)
        if required_index1 is None:
            return {}

        # ÉTAPE 3: Obtenir les 9 valeurs INDEX5 possibles pour la main n+1
        valid_index5_values = self.get_valid_index5_values(required_index1)

        # ÉTAPE 4: Calculer les différentiels pour chaque valeur INDEX5 possible à n+1
        predictive_differentials = {}

        for possible_index5 in valid_index5_values:
            try:
                # ÉTAPE 4.1: Créer une séquence simulée avec la valeur possible ajoutée à n+1
                simulated_sequence = sequence[:position+1] + [possible_index5]

                # ÉTAPE 4.2: Calculer les métriques pour la main n+1 simulée

                # Entropie conditionnelle pour la séquence simulée complète
                simulated_conditional = analyzer._calculate_conditional_entropy(simulated_sequence)

                # Entropie simple (Shannon) pour la séquence simulée complète
                # Calculer les fréquences de la séquence simulée
                from collections import Counter
                counts = Counter(simulated_sequence)
                total = len(simulated_sequence)
                probabilities = [counts[value] / total for value in counts.keys()]
                simulated_simple = analyzer._calculate_shannon_entropy(probabilities)

                # Entropie théorique (AEP) pour la séquence simulée complète
                simulated_theoretical = analyzer._calculate_sequence_entropy_aep(simulated_sequence)

                # Taux d'entropie (approximation avec blocs)
                if len(simulated_sequence) >= 3:
                    block_entropies = analyzer._calculate_block_entropies(simulated_sequence, min(5, len(simulated_sequence)))
                    if block_entropies and len(block_entropies) > 0:
                        simulated_rate = block_entropies[-1]
                    else:
                        simulated_rate = simulated_simple
                else:
                    simulated_rate = simulated_simple

                # ÉTAPE 4.3: Calculer les différentiels absolus |métrique(n+1) - métrique(n)|
                diff_cond = abs(simulated_conditional - current_conditional)
                diff_taux = abs(simulated_rate - current_rate)
                diff_div_entrop = abs(simulated_simple - current_simple)
                diff_entrop = abs(simulated_theoretical - current_theoretical)

            except Exception as e:
                # En cas d'erreur, utiliser des valeurs par défaut
                diff_cond = diff_taux = diff_div_entrop = diff_entrop = 0.0

            predictive_differentials[possible_index5] = {
                'DiffCond': diff_cond,
                'DiffTaux': diff_taux,
                'DiffDivEntropG': diff_div_entrop,
                'DiffEntropG': diff_entrop
            }

        return predictive_differentials

    def generate_predictive_table_part(self, sequence, evolution, analyzer, start_main, end_main, part_number):
        """
        Génère une partie du tableau prédictif avec différentiels et séparateurs verticaux
        """
        if not sequence or not evolution:
            return "❌ Données insuffisantes pour générer le tableau prédictif"

        # Pour le premier tableau, commencer à la main 6 au lieu de la main 1
        if part_number == 1:
            actual_start = max(start_main, 6)  # Commencer à la main 6
        else:
            actual_start = start_main

        # Ajuster les limites selon la longueur de la séquence
        actual_end = min(end_main, len(sequence))
        if actual_start > len(sequence):
            return f"❌ Main {actual_start} dépasse la longueur de la séquence ({len(sequence)} mains)"

        # En-tête du tableau avec séparateurs verticaux
        header_line1 = "INDEX5 n+1".ljust(15)
        header_line2 = " " * 15
        header_separator = " " * 15  # Séparateur horizontal sous les en-têtes Main
        separator_line = "=" * 15

        # Générer l'en-tête pour la plage de mains spécifiée avec séparateurs
        for i in range(actual_start, actual_end + 1):
            header_line1 += "|" + f"Main {i:2d}".center(24)
            # Construction exacte du format souhaité : |DiffC|DiffT|DivEG|EntG  |
            header_line2 += "|DiffC|DiffT|DivEG|EntG  ".ljust(25)
            # Séparateur horizontal sous chaque "Main X" mais pas sous "INDEX5 n+1"
            header_separator += "|" + "-" * 24
            separator_line += "|" + "=" * 24

        # Ajouter le séparateur final
        header_line1 += "|"
        header_line2 += "|"
        header_separator += "|"
        separator_line += "|"

        table = f"📊 PARTIE {part_number} - MAINS {actual_start} À {actual_end}\n"
        table += separator_line + "\n"
        table += header_line1 + "\n"
        table += header_separator + "\n"  # Séparateur horizontal entre Main et DiffC DiffT DivEG EntG
        table += header_line2 + "\n"
        table += separator_line + "\n"

        # Générer les lignes pour chaque valeur INDEX5 possible avec séparateurs
        for i, index5_value in enumerate(self.all_index5_values):
            line = index5_value.ljust(15)

            for position in range(actual_start - 1, actual_end):  # -1 car les indices commencent à 0
                line += "|"  # Séparateur vertical avant chaque main

                if position < len(sequence):
                    # Calculer les différentiels prédictifs (déjà filtrés selon les règles INDEX1)
                    predictive_diffs = self.calculate_predictive_differentials(
                        sequence, evolution, position, analyzer
                    )

                    if index5_value in predictive_diffs:
                        diffs = predictive_diffs[index5_value]
                        # Ajouter des séparateurs verticaux entre les valeurs
                        cell_content = f"{diffs['DiffCond']:5.3f}|{diffs['DiffTaux']:5.3f}|{diffs['DiffDivEntropG']:5.3f}|{diffs['DiffEntropG']:5.3f}"
                    else:
                        cell_content = "  N/A | N/A | N/A | N/A"
                else:
                    cell_content = "  --- | --- | --- | ---"

                line += cell_content.center(24)

            line += "|"  # Séparateur final
            table += line + "\n"

            # Ajouter un séparateur horizontal entre 0_C_TIE et 1_A_BANKER
            if index5_value == "0_C_TIE":
                table += separator_line + "\n"

        # Ajouter une ligne de séparation finale
        table += separator_line + "\n"

        # NOUVELLE LIGNE : Valeur INDEX5 réellement observée pour chaque main
        observed_line = "OBSERVÉ        "
        for position in range(actual_start - 1, actual_end):  # -1 car les indices commencent à 0
            observed_line += "|"  # Séparateur vertical avant chaque main

            if position < len(sequence):
                # Récupérer la valeur INDEX5 réellement observée à cette position
                observed_index5 = sequence[position]
                # Centrer la valeur observée dans la cellule
                cell_content = observed_index5
            else:
                cell_content = "---"

            observed_line += cell_content.center(24)

        observed_line += "|\n"
        table += observed_line

        # Ligne de séparation finale après la ligne observée
        table += separator_line + "\n"

        return table

    def generate_predictive_table(self, sequence, evolution, analyzer):
        """
        Génère le tableau prédictif complet divisé en deux parties
        """
        if not sequence or not evolution:
            return "❌ Données insuffisantes pour générer le tableau prédictif"

        # Générer la première partie (Mains 1-30)
        table_part1 = self.generate_predictive_table_part(sequence, evolution, analyzer, 1, 30, 1)

        # Générer la deuxième partie (Mains 31-60)
        table_part2 = self.generate_predictive_table_part(sequence, evolution, analyzer, 31, 60, 2)

        # Combiner les deux parties avec la légende
        complete_table = table_part1 + "\n\n" + table_part2 + f"""

📋 LÉGENDE DU TABLEAU PRÉDICTIF :
• DiffC = DiffCond (Différentiel Entropie Conditionnelle)
• DiffT = DiffTaux (Différentiel Taux d'Entropie)
• DivEG = DiffDivEntropG (Différentiel Diversité Entropique)
• EntG = DiffEntropG (Différentiel Entropie Générale)
• Seules les 9 valeurs INDEX5 respectant les règles INDEX1 sont calculées
• N/A = Valeur non calculable (ne respecte pas les règles INDEX1)
• --- = Main non disponible dans cette partie

🔄 RÈGLES INDEX1 DÉTERMINISTES :
• Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
• Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""

        return complete_table


class INDEX5PredictionValidator:
    """
    Classe pour valider les prédictions INDEX5 en comparant INDEX3
    Une prédiction est correcte si INDEX3 de la prédiction = INDEX3 de la réalité
    Format INDEX5: INDEX1_INDEX2_INDEX3 (ex: 0_A_BANKER → INDEX3 = BANKER)
    """

    def __init__(self):
        """Initialisation du validateur de prédictions"""
        self.correct_predictions = 0
        self.total_predictions = 0
        self.correct_predictions_high_confidence = 0  # Nouveau compteur pour poids >= 60%
        self.total_predictions_high_confidence = 0    # Total prédictions avec poids >= 60%
        self.prediction_details = []

    def extract_index3(self, index5_value):
        """
        Extrait INDEX3 d'une valeur INDEX5
        Format: INDEX1_INDEX2_INDEX3 → retourne INDEX3
        """
        if not index5_value or index5_value == "N/A":
            return None

        # Nettoyer la valeur (enlever score de confiance si présent)
        clean_value = str(index5_value).split('(')[0] if '(' in str(index5_value) else str(index5_value)

        # Diviser par underscore et prendre le dernier élément (INDEX3)
        parts = clean_value.split('_')
        if len(parts) >= 3:
            index3 = parts[2]  # INDEX3 (BANKER, PLAYER, TIE)

            # Normaliser les abréviations vers les formes complètes
            if index3 == "BANK":
                return "BANKER"
            elif index3 == "PLAY":
                return "PLAYER"
            else:
                return index3  # TIE reste TIE

        return None

    def extract_confidence(self, predicted_index5):
        """
        Extrait le score de confiance d'une prédiction
        Format: INDEX5(0.XX) → retourne 0.XX comme float
        """
        if not predicted_index5 or '(' not in predicted_index5:
            return 0.0

        try:
            # Extraire la partie entre parenthèses
            confidence_part = predicted_index5.split('(')[1].split(')')[0]
            return float(confidence_part)
        except (IndexError, ValueError):
            return 0.0

    def validate_prediction(self, predicted_index5, actual_index5, position):
        """
        Valide une prédiction en comparant les INDEX3
        Ignore les prédictions "WAIT"
        """
        # Ignorer les prédictions WAIT
        if predicted_index5 == "WAIT":
            return

        # Extraire INDEX3 de la prédiction (le nettoyage est fait dans extract_index3)
        predicted_index3 = self.extract_index3(predicted_index5)

        # Extraire INDEX3 de la valeur réelle
        actual_index3 = self.extract_index3(actual_index5)

        # Vérifier si les deux INDEX3 sont valides
        if predicted_index3 and actual_index3:
            # RÈGLE TIE: Si réalité = TIE et prédiction ≠ TIE → NE PAS COMPTER
            if actual_index3 == "TIE" and predicted_index3 != "TIE":
                # Ne pas compter cette prédiction (ni valide ni invalide)
                return

            # Extraire le score de confiance
            confidence = self.extract_confidence(predicted_index5)

            self.total_predictions += 1
            is_correct = predicted_index3 == actual_index3

            if is_correct:
                self.correct_predictions += 1

            # Compteur spécial pour poids pondéré >= 60% (seuil haute confiance)
            # RESTAURATION: Seuil basé sur poids pondéré cumulé comme dans l'ancien code
            if confidence >= 0.60:
                self.total_predictions_high_confidence += 1
                if is_correct:
                    self.correct_predictions_high_confidence += 1

            # Enregistrer les détails
            self.prediction_details.append({
                'position': position,
                'predicted_index5': predicted_index5,
                'actual_index5': actual_index5,
                'predicted_index3': predicted_index3,
                'actual_index3': actual_index3,
                'confidence': confidence,
                'is_correct': is_correct,
                'is_high_confidence': confidence >= 0.60
            })

            return is_correct

        return None  # Prédiction non validable

    def get_accuracy_stats(self):
        """
        Retourne les statistiques de précision
        """
        if self.total_predictions == 0:
            return {
                'correct_predictions': 0,
                'total_predictions': 0,
                'accuracy_percentage': 0.0,
                'accuracy_ratio': "0/0",
                'correct_predictions_high_confidence': 0,
                'total_predictions_high_confidence': 0,
                'accuracy_percentage_high_confidence': 0.0,
                'accuracy_ratio_high_confidence': "0/0"
            }

        accuracy = (self.correct_predictions / self.total_predictions) * 100

        # Calculer la précision pour les prédictions haute confiance (>= 60% poids pondéré)
        accuracy_high_confidence = 0.0
        if self.total_predictions_high_confidence > 0:
            accuracy_high_confidence = (self.correct_predictions_high_confidence / self.total_predictions_high_confidence) * 100

        return {
            'correct_predictions': self.correct_predictions,
            'total_predictions': self.total_predictions,
            'accuracy_percentage': accuracy,
            'accuracy_ratio': f"{self.correct_predictions}/{self.total_predictions}",
            'correct_predictions_high_confidence': self.correct_predictions_high_confidence,
            'total_predictions_high_confidence': self.total_predictions_high_confidence,
            'accuracy_percentage_high_confidence': accuracy_high_confidence,
            'accuracy_ratio_high_confidence': f"{self.correct_predictions_high_confidence}/{self.total_predictions_high_confidence}"
        }

    def get_detailed_report(self):
        """
        Retourne un rapport détaillé des prédictions
        """
        stats = self.get_accuracy_stats()

        report = f"""
🎯 VALIDATION DES PRÉDICTIONS INDEX5 - COMPARAISON INDEX3
═══════════════════════════════════════════════════════

📊 STATISTIQUES GLOBALES:
• Prédictions correctes: {stats['correct_predictions']}
• Total prédictions: {stats['total_predictions']}
• Taux de réussite: {stats['accuracy_percentage']:.2f}%
• Ratio: {stats['accuracy_ratio']}

🎯 STATISTIQUES HAUTE CONFIANCE (≥ 60% poids pondéré):
• Prédictions correctes (≥60%): {stats['correct_predictions_high_confidence']}
• Total prédictions (≥60%): {stats['total_predictions_high_confidence']}
• Taux de réussite (≥60%): {stats['accuracy_percentage_high_confidence']:.2f}%
• Ratio (≥60%): {stats['accuracy_ratio_high_confidence']}

🔍 MÉTHODE DE VALIDATION:
• Format INDEX5: INDEX1_INDEX2_INDEX3
• Validation: INDEX3_prédit = INDEX3_réel
• Exemple: 0_A_BANKER → INDEX3 = BANKER
• Confiance haute: Score ≥ 0.60 (60%)
"""

        if self.prediction_details:
            report += "\n📋 DÉTAIL DES PRÉDICTIONS:\n"
            report += "Position | Prédiction → Réalité | INDEX3 Prédit → INDEX3 Réel | Confiance | Résultat\n"
            report += "---------|---------------------|---------------------------|-----------|----------\n"

            for detail in self.prediction_details[-10:]:  # Afficher les 10 dernières
                result_symbol = "✅" if detail['is_correct'] else "❌"
                confidence_symbol = "🎯" if detail.get('is_high_confidence', False) else "📊"
                confidence_display = f"{detail.get('confidence', 0.0):.2f}"
                report += f"Main {detail['position']:2d}  | {detail['predicted_index5']:12s} → {detail['actual_index5']:12s} | {detail['predicted_index3']:6s} → {detail['actual_index3']:6s} | {confidence_symbol}{confidence_display:5s} | {result_symbol}\n"

        return report

    def reset(self):
        """
        Remet à zéro les compteurs
        """
        self.correct_predictions = 0
        self.total_predictions = 0
        self.correct_predictions_high_confidence = 0
        self.total_predictions_high_confidence = 0
        self.prediction_details = []


def main():
    """
    Fonction principale pour l'analyse d'entropie du baccarat.

    Référence: entropie/cours_entropie/niveau_debutant/02_formule_shannon.md
    Application de la formule H(X) = -∑ p(x) log₂ p(x) au baccarat INDEX5
    """
    print("🎰 ANALYSEUR D'ENTROPIE BACCARAT - INDEX5")
    print("=" * 50)
    print("Basé sur les formules d'entropie de Shannon")
    print("Référence: entropie/cours_entropie/")
    print()

    # Initialisation de l'analyseur
    analyzer = BaccaratEntropyAnalyzer()

    # Chargement des données
    filepath = "partie/dataset_baccarat_lupasco_20250704_092825_condensed.json"
    data = analyzer.load_baccarat_data(filepath)

    if not data:
        print("❌ Impossible de charger les données. Vérifiez le fichier.")
        return

    print(f"📊 {len(data)} parties chargées avec succès")
    print()

    # Menu interactif
    non_interactive_mode = False

    while True:
        print("\n🎯 OPTIONS D'ANALYSE:")
        print("1. Analyser une partie spécifique")
        print("2. Analyser toutes les parties")
        print("3. Analyser les N premières parties")
        print("4. Afficher les statistiques théoriques")
        print("5. Quitter")

        try:
            choice = input("\nChoisissez une option (1-5): ").strip()
        except EOFError:
            # Mode non-interactif : analyser la partie 1 par défaut
            print("Mode non-interactif détecté. Analyse de la partie 1...")
            choice = '1'
            non_interactive_mode = True

        if choice == '1':
            # Analyse d'une partie spécifique
            try:
                if non_interactive_mode:
                    game_index = 0  # Partie 1 par défaut
                else:
                    try:
                        game_index = int(input(f"Numéro de la partie (1-{len(data)}): ")) - 1
                    except EOFError:
                        print("Mode non-interactif détecté. Analyse de la partie 1...")
                        game_index = 0
                        non_interactive_mode = True
                if 0 <= game_index < len(data):
                    print(f"\n🔍 Analyse de la partie {game_index + 1}...")

                    result = analyzer.analyze_single_game(data[game_index], f"Partie_{game_index + 1}")

                    if 'error' in result:
                        print(f"❌ Erreur: {result['error']}")
                    else:
                        # Export automatique du rapport complet
                        rapport_filename = f"rapport{game_index + 1}.txt"
                        rapport_complet = analyzer.generate_entropy_report(result)

                        try:
                            with open(rapport_filename, 'w', encoding='utf-8') as f:
                                f.write(rapport_complet)
                            print(f"📄 Rapport exporté automatiquement : {rapport_filename}")
                        except Exception as e:
                            print(f"❌ Erreur lors de l'export du rapport : {e}")

                        # Proposition de visualisation (avec gestion EOFError)
                        try:
                            show_plot = input("\nAfficher le graphique d'évolution? (o/n): ").lower() == 'o'
                            if show_plot:
                                # Génération automatique du nom de fichier JPG
                                jpg_filename = f"graphique_entropie_partie_{game_index + 1}.jpg"
                                analyzer.plot_entropy_evolution(result, save_path=jpg_filename)

                            # Proposition d'export CSV
                            export_csv = input("Exporter vers CSV? (o/n): ").lower() == 'o'
                            if export_csv:
                                filename = f"entropy_analysis_partie_{game_index + 1}.csv"
                                analyzer.export_results_to_csv(result, filename)
                        except EOFError:
                            print("Mode non-interactif : pas de visualisation ni d'export")

                        # En mode non-interactif, sortir après l'analyse
                        if non_interactive_mode:
                            print("\n✅ Analyse terminée en mode non-interactif")
                            return
                else:
                    print("❌ Numéro de partie invalide")
            except ValueError:
                print("❌ Veuillez entrer un numéro valide")

        elif choice == '2':
            # Analyse de toutes les parties
            print("\n🔍 Analyse de toutes les parties...")
            try:
                confirm = input("⚠️  Cela peut prendre du temps. Continuer? (o/n): ").lower()
            except EOFError:
                print("Mode non-interactif : analyse annulée")
                return

            if confirm == 'o':
                global_stats = analyzer.analyze_multiple_games(data)

                if 'error' in global_stats:
                    print(f"❌ Erreur: {global_stats['error']}")
                else:
                    print(f"\n📊 STATISTIQUES GLOBALES ({global_stats['total_games_analyzed']} parties)")
                    print("=" * 60)
                    print(f"Entropie finale moyenne: {global_stats['average_final_entropy']:.4f} ± {global_stats['std_final_entropy']:.4f} bits")
                    print(f"Entropie finale min/max: {global_stats['min_final_entropy']:.4f} / {global_stats['max_final_entropy']:.4f} bits")
                    print(f"Longueur moyenne des parties: {global_stats['average_sequence_length']:.1f} mains")
                    print(f"Position moyenne du max d'entropie: {global_stats['average_max_entropy_position']:.1f}")

        elif choice == '3':
            # Analyse des N premières parties
            try:
                n_games = int(input(f"Nombre de parties à analyser (max {len(data)}): "))
                if 1 <= n_games <= len(data):
                    print(f"\n🔍 Analyse des {n_games} premières parties...")

                    global_stats = analyzer.analyze_multiple_games(data, n_games)

                    if 'error' in global_stats:
                        print(f"❌ Erreur: {global_stats['error']}")
                    else:
                        print(f"\n📊 STATISTIQUES ({global_stats['total_games_analyzed']} parties)")
                        print("=" * 50)
                        print(f"Entropie finale moyenne: {global_stats['average_final_entropy']:.4f} ± {global_stats['std_final_entropy']:.4f} bits")
                        print(f"Entropie finale min/max: {global_stats['min_final_entropy']:.4f} / {global_stats['max_final_entropy']:.4f} bits")
                        print(f"Longueur moyenne des parties: {global_stats['average_sequence_length']:.1f} mains")
                else:
                    print("❌ Nombre invalide")
            except ValueError:
                print("❌ Veuillez entrer un nombre valide")
            except EOFError:
                print("Mode non-interactif : analyse annulée")
                return

        elif choice == '4':
            # Statistiques théoriques
            print(f"\n📋 STATISTIQUES THÉORIQUES INDEX5")
            print("=" * 40)
            print(f"Nombre total de valeurs possibles: 18")
            print(f"Entropie théorique maximale: {analyzer.theoretical_entropy:.4f} bits")
            print(f"Entropie uniforme (18 valeurs): {np.log2(18):.4f} bits")
            print(f"\n🎯 Probabilités théoriques:")
            for value, prob in sorted(analyzer.theoretical_probs.items()):
                print(f"  {value}: {prob:.4f} ({prob*100:.2f}%)")

        elif choice == '5':
            print("👋 Au revoir!")
            break

        else:
            print("❌ Option invalide. Choisissez entre 1 et 5.")

if __name__ == "__main__":
    main()
