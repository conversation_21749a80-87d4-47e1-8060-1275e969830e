# 🟡 NIVEAU INTERMÉDIAIRE - Chapitre 1
## Entropie Relative et Divergence de Kullback-Leibler

### 🎯 Objectifs de ce chapitre
- Maîtriser l'entropie relative (divergence KL)
- Comprendre ses propriétés mathématiques fondamentales
- Appliquer la divergence KL à des problèmes concrets
- Établir les liens avec l'optimisation et l'apprentissage automatique

---

## 📐 Définition Mathématique

### Formule de la Divergence KL

Pour deux distributions de probabilité p et q sur le même ensemble E :

```
D(p||q) = ∑ p(x) log₂(p(x)/q(x))
         x∈E
```

**Notation** : D(p||q) se lit "divergence de p vers q" ou "KL de p par rapport à q"

### Décomposition Détaillée

**Chaque terme expliqué** :
- **D(p||q)** : Divergence de Kullback-Leibler
- **p(x)** : Probabilité selon la distribution "vraie" p
- **q(x)** : Probabilité selon la distribution "approximative" q
- **p(x)/q(x)** : Ratio des probabilités
- **log₂** : Logarithme en base 2 (mesure en bits)
- **∑** : Somme sur tous les événements possibles

### Interprétation Physique

> **D(p||q) mesure l'inefficacité de supposer que la distribution est q quand elle est réellement p.**

**Analogies** :
- **Distance informationnelle** : "À quel point q est loin de p"
- **Coût de l'erreur** : "Prix à payer pour utiliser q au lieu de p"
- **Surprise supplémentaire** : "Information supplémentaire nécessaire"

---

## 🧮 Calculs Détaillés

### Exemple 1 : Distributions de Bernoulli

**Situation** : Comparer deux pièces de monnaie
- p : Pièce équilibrée (0.5, 0.5)
- q : Pièce biaisée (0.7, 0.3)

**Calcul de D(p||q)** :
```
D(p||q) = 0.5 × log₂(0.5/0.7) + 0.5 × log₂(0.5/0.3)
        = 0.5 × log₂(0.714) + 0.5 × log₂(1.667)
        = 0.5 × (-0.487) + 0.5 × (0.737)
        = -0.244 + 0.369
        = 0.125 bits
```

**Calcul de D(q||p)** :
```
D(q||p) = 0.7 × log₂(0.7/0.5) + 0.3 × log₂(0.3/0.5)
        = 0.7 × log₂(1.4) + 0.3 × log₂(0.6)
        = 0.7 × 0.485 + 0.3 × (-0.737)
        = 0.340 - 0.221
        = 0.119 bits
```

**Observation** : D(p||q) ≠ D(q||p) → **Non-symétrie**

### Exemple 2 : Cas Extrême

**Situation** : q(x) = 0 mais p(x) > 0 pour un certain x

**Résultat** : D(p||q) = +∞

**Interprétation** : Il est infiniment coûteux d'ignorer un événement possible.

### Exemple 3 : Distributions Identiques

**Situation** : p = q

**Calcul** :
```
D(p||p) = ∑ p(x) log₂(p(x)/p(x)) = ∑ p(x) log₂(1) = ∑ p(x) × 0 = 0
```

**Interprétation** : Aucun coût si les distributions sont identiques.

---

## 📊 Propriétés Fondamentales

### Propriété 1 : Positivité (Inégalité de Gibbs)

**Théorème** : D(p||q) ≥ 0 avec égalité si et seulement si p = q

**Preuve intuitive** : Utilise l'inégalité de Jensen avec la fonction convexe x log x.

**Importance** : Base de nombreuses preuves en théorie de l'information.

### Propriété 2 : Non-Symétrie

**Fait** : En général, D(p||q) ≠ D(q||p)

**Exemple numérique** : Voir l'exemple 1 ci-dessus.

**Conséquence** : Ce n'est pas une distance au sens mathématique strict.

### Propriété 3 : Convexité

**Théorème** : D(p||q) est convexe en (p,q)

**Application** : Garantit l'existence d'optimums globaux dans les problèmes d'optimisation.

### Propriété 4 : Invariance par Reparamétrisation

**Fait** : D(p||q) ne dépend que des distributions, pas de la représentation.

---

## 🔗 Relations avec d'Autres Concepts

### Relation avec l'Entropie Croisée

**Formule** : D(p||q) = H(p,q) - H(p)

Où :
- **H(p,q)** = -∑ p(x) log₂(q(x)) (entropie croisée)
- **H(p)** = -∑ p(x) log₂(p(x)) (entropie de Shannon)

**Interprétation** : La divergence KL est l'excès d'entropie croisée par rapport à l'entropie vraie.

### Relation avec l'Information Mutuelle

**Formule** : I(X;Y) = D(p(x,y)||p(x)p(y))

**Interprétation** : L'information mutuelle mesure à quel point la distribution jointe s'écarte de l'indépendance.

### Relation avec la Vraisemblance

**Formule** : D(p||q) = E_p[log p(X) - log q(X)]

**Interprétation** : Différence moyenne des log-vraisemblances.

---

## 🎯 Applications Pratiques

### 1. Apprentissage Automatique

#### Classification (Fonction de Perte)

**Contexte** : Entraîner un classificateur
- p : Distribution vraie des classes
- q : Distribution prédite par le modèle

**Objectif** : Minimiser D(p||q) pour améliorer les prédictions

**Exemple** : Reconnaissance d'images
```python
# Pseudo-code
true_labels = [0, 1, 0, 1, 1]  # Classes vraies
predicted_probs = [[0.9, 0.1], [0.3, 0.7], [0.8, 0.2], [0.2, 0.8], [0.1, 0.9]]

# La fonction de perte est basée sur D(p||q)
loss = cross_entropy(true_labels, predicted_probs)
```

#### Sélection de Modèles

**Principe** : Choisir le modèle q qui minimise D(p||q)

**Critères** :
- AIC (Akaike Information Criterion)
- BIC (Bayesian Information Criterion)

### 2. Traitement du Signal

#### Codage de Source

**Problème** : Compresser des données
- p : Distribution vraie des symboles
- q : Distribution supposée par l'algorithme de compression

**Résultat** : La longueur moyenne de codage est H(p) + D(p||q)

**Optimisation** : Minimiser D(p||q) pour une compression optimale

#### Détection d'Anomalies

**Principe** : Détecter quand la distribution change
- p : Distribution normale
- q : Distribution observée

**Alerte** : Si D(p||q) > seuil, alors anomalie détectée

### 3. Statistiques et Tests

#### Test d'Hypothèses

**Contexte** : Tester si des données suivent une distribution théorique
- H₀ : Les données suivent la distribution q
- H₁ : Les données suivent une autre distribution p

**Statistique** : Utiliser D(p̂||q) où p̂ est la distribution empirique

#### Estimation de Densité

**Principe** : Trouver la meilleure approximation q d'une distribution inconnue p

**Méthode** : Minimiser D(p||q) par maximum de vraisemblance

---

## 🧪 Variantes et Extensions

### Divergence KL Symétrisée

**Formule** : D_sym(p,q) = D(p||q) + D(q||p)

**Propriété** : Symétrique mais toujours pas une distance (inégalité triangulaire non respectée)

### Distance de Jensen-Shannon

**Formule** : JS(p,q) = ½D(p||m) + ½D(q||m) où m = ½(p+q)

**Propriétés** :
- Symétrique
- Bornée : 0 ≤ JS(p,q) ≤ 1
- Satisfait l'inégalité triangulaire

### Divergences f-Divergences

**Famille générale** : D_f(p||q) = ∑ q(x) f(p(x)/q(x))

**Cas particuliers** :
- f(t) = t log t → Divergence KL
- f(t) = (t-1)² → Divergence χ²
- f(t) = |t-1| → Variation totale

---

## 🎯 Exercices Pratiques

### Exercice 1 : Calcul Direct

Calculez D(p||q) et D(q||p) pour :
- p = (0.6, 0.4)
- q = (0.8, 0.2)

**Solution** :
```
D(p||q) = 0.6×log₂(0.6/0.8) + 0.4×log₂(0.4/0.2)
        = 0.6×log₂(0.75) + 0.4×log₂(2)
        = 0.6×(-0.415) + 0.4×1
        = -0.249 + 0.4 = 0.151 bits

D(q||p) = 0.8×log₂(0.8/0.6) + 0.2×log₂(0.2/0.4)
        = 0.8×log₂(1.333) + 0.2×log₂(0.5)
        = 0.8×0.415 + 0.2×(-1)
        = 0.332 - 0.2 = 0.132 bits
```

### Exercice 2 : Application ML

**Contexte** : Classificateur binaire
- Vraies probabilités : p = (0.7, 0.3)
- Prédictions modèle 1 : q₁ = (0.8, 0.2)
- Prédictions modèle 2 : q₂ = (0.6, 0.4)

**Question** : Quel modèle est meilleur selon la divergence KL ?

### Exercice 3 : Optimisation

**Problème** : Trouver q = (q, 1-q) qui minimise D(p||q) avec p = (0.3, 0.7)

**Méthode** : Dériver D(p||q) par rapport à q et égaler à zéro.

---

## 🔍 Aspects Computationnels

### Stabilité Numérique

**Problème** : log(0) et division par 0

**Solutions** :
1. **Régularisation** : Ajouter ε petit aux probabilités nulles
2. **Masquage** : Ignorer les termes où p(x) = 0
3. **Limite** : Utiliser lim_{x→0} x log x = 0

### Complexité Algorithmique

**Calcul direct** : O(n) où n est le nombre d'événements

**Optimisation** : Utiliser les propriétés de convexité pour l'optimisation efficace

### Implémentation Python

```python
import numpy as np

def kl_divergence(p, q, epsilon=1e-12):
    """Calcule D(p||q) avec protection numérique"""
    p = np.array(p) + epsilon
    q = np.array(q) + epsilon
    return np.sum(p * np.log2(p / q))
```

---

## 🔗 Vers le Chapitre Suivant

**Concepts maîtrisés** :
✅ Divergence KL et ses propriétés  
✅ Applications en ML et statistiques  
✅ Calculs pratiques et stabilité numérique  

**Prochaine étape** :
- Théorème de codage de source
- Inégalité de Kraft
- Codes optimaux et compression

---

## 📚 Points Clés à Retenir

✅ **D(p||q) ≥ 0** avec égalité ssi p = q  
✅ **Non-symétrique** : D(p||q) ≠ D(q||p)  
✅ **Convexe** : Garantit l'optimisation globale  
✅ **Applications** : ML, compression, tests statistiques  
✅ **Relation** : D(p||q) = H(p,q) - H(p)  

---

*Prochaine étape : [Chapitre 2 - Théorème de Codage de Source](02_codage_source.md)*
