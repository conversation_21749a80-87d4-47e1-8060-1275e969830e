# 🔴 NIVEAU EXPERT - Chapitre 1
## Entropie Métrique et Systèmes Dynamiques

### 🎯 Objectifs de ce chapitre
- Maîtriser l'entropie métrique de Kolmogorov-Sinai
- Comprendre les liens avec la théorie ergodique
- Étudier les applications aux systèmes dynamiques
- Analyser les propriétés topologiques et mesurables

---

## 📐 Définitions Rigoureuses

### Entropie Métrique (Kolmogorov-Sinai)

**Contexte** : Soit (X, ℬ, μ, T) un système dynamique où :
- X : Espace de phases compact
- ℬ : σ-algèbre borélienne
- μ : Mesure de probabilité T-invariante
- T : Transformation préservant la mesure

**Définition** : Pour une partition mesurable α = {A₁, A₂, ..., Aₖ} :

```
h_μ(T, α) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
```

**Entropie métrique** :
```
h_μ(T) = sup{h_μ(T, α) : α partition finie}
```

### Décomposition Mathématique

**H_μ(α)** : Entropie de la partition α
```
H_μ(α) = -∑ᵢ μ(Aᵢ) log μ(Aᵢ)
```

**⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα** : Partition jointe
```
⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα = {⋂ᵢ₌₀ⁿ⁻¹ T⁻ⁱAⱼᵢ : j₀,...,jₙ₋₁ ∈ {1,...,k}}
```

**Interprétation** : Raffinement de la partition par les itérées de T

---

## 🧮 Calculs Fondamentaux

### Exemple 1 : Décalage de Bernoulli

**Système** : B(p₁, p₂, ..., pₖ) sur Σ = {1,2,...,k}ℤ

**Transformation** : σ(x)ₙ = xₙ₊₁ (décalage à gauche)

**Mesure** : μ = ∏ᵢ₌₋∞^∞ νᵢ où νᵢ = ν = (p₁, p₂, ..., pₖ)

**Partition naturelle** : α = {[1], [2], ..., [k]}
où [i] = {x ∈ Σ : x₀ = i}

**Calcul de l'entropie** :
```
h_μ(σ, α) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ σ⁻ⁱα)
```

**Résultat** : h_μ(σ) = H(ν) = -∑ᵢ pᵢ log pᵢ

### Exemple 2 : Rotation Irrationnelle

**Système** : T_α : x ↦ x + α (mod 1) sur le cercle S¹

**Condition** : α irrationnel

**Mesure** : μ = mesure de Lebesgue

**Théorème** : h_μ(T_α) = 0

**Preuve esquisse** : Toute partition finie a une entropie conditionnelle qui tend vers 0.

### Exemple 3 : Applications Dilatantes

**Système** : T : x ↦ dx (mod 1) avec d ≥ 2 entier

**Mesure** : μ = mesure de Lebesgue

**Calcul** : Pour la partition α = {[0, 1/d), [1/d, 2/d), ..., [(d-1)/d, 1)}

**Résultat** : h_μ(T) = log d

---

## 📊 Propriétés Fondamentales

### Théorème 1 : Invariance par Isomorphisme

**Énoncé** : Si (X, μ, T) et (Y, ν, S) sont isomorphes, alors h_μ(T) = h_ν(S)

**Preuve** : L'isomorphisme préserve les partitions et leurs entropies.

### Théorème 2 : Additivité

**Énoncé** : h_μ×ν(T × S) = h_μ(T) + h_ν(S)

**Application** : Permet de calculer l'entropie de produits de systèmes.

### Théorème 3 : Formule de Rokhlin

**Énoncé** : Pour un système ergodique,
```
h_μ(T) = lim_{n→∞} H_μ(Xₙ | X₀, X₁, ..., Xₙ₋₁)
```

**Interprétation** : L'entropie métrique est l'incertitude asymptotique conditionnelle.

### Théorème 4 : Principe Variationnel

**Énoncé** : h_top(T) = sup{h_μ(T) : μ T-invariante}

où h_top(T) est l'entropie topologique.

---

## 🔬 Théorie Ergodique Avancée

### Théorème de Shannon-McMillan-Breiman

**Contexte** : Processus stationnaire ergodique (Xₙ)ₙ∈ℤ

**Énoncé** : Pour μ-presque tout x,
```
lim_{n→∞} -(1/n) log μ([X₀, X₁, ..., Xₙ₋₁](x)) = h_μ(T)
```

**Interprétation** : Convergence ponctuelle du contenu informationnel vers l'entropie métrique.

### Ensembles Typiques Dynamiques

**Définition** : Pour ε > 0,
```
T_n^ε = {x : |-(1/n) log μ([X₀,...,Xₙ₋₁](x)) - h_μ(T)| < ε}
```

**Propriétés** :
1. μ(T_n^ε) → 1 quand n → ∞
2. |T_n^ε| ≤ 2^{n(h_μ(T) + ε)}
3. μ(A ∩ T_n^ε) ≥ (1-ε)2^{-n(h_μ(T) + ε)} pour A ⊂ T_n^ε

### Applications à la Complexité

**Complexité par blocs** : p_n(x) = |{T^k x : 0 ≤ k < n}|

**Théorème** : Pour un système minimal,
```
h_top(T) = lim_{n→∞} (1/n) log p_n(x)
```

---

## 🎯 Applications Spécialisées

### 1. Systèmes Hyperboliques

#### Décalages Sofiques

**Définition** : Sous-décalage défini par un ensemble fini de mots interdits

**Propriété** : h_top(X) = log λ où λ est le rayon spectral de la matrice de transition

**Exemple** : Décalage du carré doré
- Mots interdits : {11}
- Matrice : A = [0 1; 1 1]
- h_top = log((1+√5)/2) = log φ

#### Applications Expansives

**Théorème** : Pour T expansive sur un espace compact,
```
h_top(T) = sup{h_μ(T) : μ mesure d'équilibre}
```

### 2. Billards Dynamiques

#### Billards Dispersifs

**Propriété** : Entropie métrique positive pour la mesure de Liouville

**Calcul** : Utilisation des exposants de Lyapunov
```
h_μ(T) = ∑ λᵢ⁺
```
où λᵢ⁺ sont les exposants de Lyapunov positifs.

### 3. Systèmes Hamiltoniens

#### Théorème de Pesin

**Énoncé** : Pour un difféomorphisme préservant une mesure lisse,
```
h_μ(T) ≤ ∑ λᵢ⁺
```

**Égalité** : Atteinte pour les systèmes de Bernoulli.

---

## 🧪 Méthodes de Calcul Avancées

### Méthode des Partitions Génératrices

**Définition** : Une partition α est génératrice si
```
⋁ₙ₌₋∞^∞ T^n α = ℬ (mod μ)
```

**Théorème** : Si α est génératrice, alors h_μ(T) = h_μ(T, α)

**Avantage** : Réduit le calcul à une seule partition.

### Méthode Spectrale

**Principe** : Utiliser l'opérateur de transfert L_T

**Formule** : h_top(T) = log r(L_T) où r est le rayon spectral

**Application** : Systèmes expansifs par morceaux

### Approximation Numérique

**Algorithme de Grassberger-Procaccia** :
1. Choisir une partition fine
2. Calculer H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα) pour n croissant
3. Estimer la limite (1/n)H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)

---

## 🎯 Exercices Avancés

### Exercice 1 : Calcul Direct

**Système** : Décalage de Markov avec matrice de transition
```
P = [1/2  1/2]
    [1/3  2/3]
```

**Questions** :
1. Calculer la mesure stationnaire μ
2. Déterminer h_μ(σ)
3. Comparer avec h_top(σ)

### Exercice 2 : Système Codé

**Transformation** : T(x) = 2x (mod 1) sur [0,1)

**Partition** : α = {[0, 1/2), [1/2, 1)}

**Tâches** :
1. Montrer que α est génératrice
2. Calculer h_λ(T) où λ est la mesure de Lebesgue
3. Construire le codage symbolique

### Exercice 3 : Entropie Relative

**Problème** : Comparer deux mesures invariantes μ et ν pour le même système T

**Objectif** : Étudier la relation entre h_μ(T), h_ν(T) et D(μ||ν)

---

## 🔍 Développements Récents

### Entropie Métrique Relative

**Définition** : h_μ(T|S) pour deux transformations T et S

**Applications** : Théorie de l'information quantique, systèmes couplés

### Entropie Métrique Conditionnelle

**Contexte** : Systèmes avec structure de produit

**Formule** : h_μ(T|ℱ) où ℱ est une sous-σ-algèbre

### Entropie Métrique Topologique

**Généralisation** : Entropie pour les actions de groupes

**Applications** : Systèmes multi-dimensionnels, actions ℤᵈ

---

## 🔗 Connexions Interdisciplinaires

### Physique Statistique

**Entropie thermodynamique** : Lien via le principe variationnel

**Mécanique statistique** : Entropie de Kolmogorov-Sinai et chaos

### Théorie de l'Information

**Codage de sources** : Entropie métrique et compression optimale

**Complexité algorithmique** : Liens avec la complexité de Kolmogorov

### Géométrie Différentielle

**Flots géodésiques** : Entropie et courbure négative

**Systèmes Anosov** : Entropie et dimension fractale

---

## 📚 Points Clés à Retenir

✅ **h_μ(T) = sup_α h_μ(T,α)** : Supremum sur toutes les partitions  
✅ **Invariance** : Propriété fondamentale pour la classification  
✅ **Additivité** : h_μ×ν(T×S) = h_μ(T) + h_ν(S)  
✅ **SMB** : Convergence ponctuelle du contenu informationnel  
✅ **Applications** : Systèmes hyperboliques, billards, Hamiltoniens  

---

*Prochaine étape : [Chapitre 2 - Entropie Topologique](02_entropie_topologique.md)*
